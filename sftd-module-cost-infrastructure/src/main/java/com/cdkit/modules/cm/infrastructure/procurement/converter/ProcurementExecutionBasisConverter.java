package com.cdkit.modules.cm.infrastructure.procurement.converter;

import com.cdkit.modules.cm.domain.procurement.mode.entity.ProcurementExecutionBasisDetailEntity;
import com.cdkit.modules.cm.domain.procurement.mode.entity.ProcurementExecutionBasisEntity;
import com.cdkit.modules.cm.infrastructure.procurement.po.ProcurementExecutionBasis;
import com.cdkit.modules.cm.infrastructure.procurement.po.ProcurementExecutionBasisDetail;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.stream.Collectors;

/**
 * 采购执行依据转换器
 * 
 * <AUTHOR>
 * @date 2025-08-21
 */
@Component
public class ProcurementExecutionBasisConverter {

    /**
     * 领域实体转基础设施实体（主表）
     */
    public ProcurementExecutionBasis toInfrastructure(ProcurementExecutionBasisEntity domainEntity) {
        if (domainEntity == null) {
            return null;
        }

        return ProcurementExecutionBasis.builder()
                .id(domainEntity.getId())
                .materialCode(domainEntity.getMaterialCode())
                .materialName(domainEntity.getMaterialName())
                .unit(domainEntity.getUnit())
                .quarter(domainEntity.getQuarter())
                .startDate(domainEntity.getStartDate())
                .endDate(domainEntity.getEndDate())
                .totalDemandQuantity(domainEntity.getTotalDemandQuantity())
                .unitPriceExcludingTax(domainEntity.getUnitPriceExcludingTax())
                .totalPriceExcludingTax(domainEntity.getTotalPriceExcludingTax())
                .totalPriceIncludingTax(domainEntity.getTotalPriceIncludingTax())
                .purchasedQuantity(domainEntity.getPurchasedQuantity())
                .spentAmountExcludingTax(domainEntity.getSpentAmountExcludingTax())
                .spentAmountIncludingTax(domainEntity.getSpentAmountIncludingTax())
                .remainingAmountExcludingTax(domainEntity.getRemainingAmountExcludingTax())
                .remainingAmountIncludingTax(domainEntity.getRemainingAmountIncludingTax())
                .generationStatus(domainEntity.getGenerationStatus())
                .sourceQuarterlyBudgetCount(domainEntity.getSourceQuarterlyBudgetCount())
                .tenantId(domainEntity.getTenantId())
                .createTime(domainEntity.getCreateTime())
                .createBy(domainEntity.getCreateBy())
                .updateTime(domainEntity.getUpdateTime())
                .updateBy(domainEntity.getUpdateBy())
                .delFlag(domainEntity.getDelFlag())
                .sysOrgCode(domainEntity.getSysOrgCode())
                .build();
    }

    /**
     * 基础设施实体转领域实体（主表）
     */
    public ProcurementExecutionBasisEntity toDomain(ProcurementExecutionBasis infrastructureEntity) {
        if (infrastructureEntity == null) {
            return null;
        }

        return ProcurementExecutionBasisEntity.builder()
                .id(infrastructureEntity.getId())
                .materialCode(infrastructureEntity.getMaterialCode())
                .materialName(infrastructureEntity.getMaterialName())
                .unit(infrastructureEntity.getUnit())
                .quarter(infrastructureEntity.getQuarter())
                .startDate(infrastructureEntity.getStartDate())
                .endDate(infrastructureEntity.getEndDate())
                .totalDemandQuantity(infrastructureEntity.getTotalDemandQuantity())
                .unitPriceExcludingTax(infrastructureEntity.getUnitPriceExcludingTax())
                .totalPriceExcludingTax(infrastructureEntity.getTotalPriceExcludingTax())
                .totalPriceIncludingTax(infrastructureEntity.getTotalPriceIncludingTax())
                .purchasedQuantity(infrastructureEntity.getPurchasedQuantity())
                .spentAmountExcludingTax(infrastructureEntity.getSpentAmountExcludingTax())
                .spentAmountIncludingTax(infrastructureEntity.getSpentAmountIncludingTax())
                .remainingAmountExcludingTax(infrastructureEntity.getRemainingAmountExcludingTax())
                .remainingAmountIncludingTax(infrastructureEntity.getRemainingAmountIncludingTax())
                .generationStatus(infrastructureEntity.getGenerationStatus())
                .sourceQuarterlyBudgetCount(infrastructureEntity.getSourceQuarterlyBudgetCount())
                .tenantId(infrastructureEntity.getTenantId())
                .createTime(infrastructureEntity.getCreateTime())
                .createBy(infrastructureEntity.getCreateBy())
                .updateTime(infrastructureEntity.getUpdateTime())
                .updateBy(infrastructureEntity.getUpdateBy())
                .delFlag(infrastructureEntity.getDelFlag())
                .sysOrgCode(infrastructureEntity.getSysOrgCode())
                .build();
    }

    /**
     * 领域实体转基础设施实体（明细）
     */
    public ProcurementExecutionBasisDetail toInfrastructureDetail(ProcurementExecutionBasisDetailEntity domainEntity) {
        if (domainEntity == null) {
            return null;
        }

        return ProcurementExecutionBasisDetail.builder()
                .id(domainEntity.getId())
                .executionBasisId(domainEntity.getExecutionBasisId())
                .quarterlyBudgetId(domainEntity.getQuarterlyBudgetId())
                .quarterlyBudgetNo(domainEntity.getQuarterlyBudgetNo())
                .quarterlyBudgetName(domainEntity.getQuarterlyBudgetName())
                .demandQuantity(domainEntity.getDemandQuantity())
                .unit(domainEntity.getUnit())
                .unitPriceExcludingTax(domainEntity.getUnitPriceExcludingTax())
                .totalPriceExcludingTax(domainEntity.getTotalPriceExcludingTax())
                .totalPriceIncludingTax(domainEntity.getTotalPriceIncludingTax())
                .purchasedQuantity(domainEntity.getPurchasedQuantity())
                .spentAmountExcludingTax(domainEntity.getSpentAmountExcludingTax())
                .spentAmountIncludingTax(domainEntity.getSpentAmountIncludingTax())
                .remainingAmountExcludingTax(domainEntity.getRemainingAmountExcludingTax())
                .remainingAmountIncludingTax(domainEntity.getRemainingAmountIncludingTax())
                .materialCode(domainEntity.getMaterialCode())
                .materialName(domainEntity.getMaterialName())
                .compilationBasis(domainEntity.getCompilationBasis())
                .remark(domainEntity.getRemark())
                .tenantId(domainEntity.getTenantId())
                .createTime(domainEntity.getCreateTime())
                .createBy(domainEntity.getCreateBy())
                .updateTime(domainEntity.getUpdateTime())
                .updateBy(domainEntity.getUpdateBy())
                .delFlag(domainEntity.getDelFlag())
                .sysOrgCode(domainEntity.getSysOrgCode())
                .build();
    }

    /**
     * 基础设施实体转领域实体（明细）
     */
    public ProcurementExecutionBasisDetailEntity toDomainDetail(ProcurementExecutionBasisDetail infrastructureEntity) {
        if (infrastructureEntity == null) {
            return null;
        }

        return ProcurementExecutionBasisDetailEntity.builder()
                .id(infrastructureEntity.getId())
                .executionBasisId(infrastructureEntity.getExecutionBasisId())
                .quarterlyBudgetId(infrastructureEntity.getQuarterlyBudgetId())
                .quarterlyBudgetNo(infrastructureEntity.getQuarterlyBudgetNo())
                .quarterlyBudgetName(infrastructureEntity.getQuarterlyBudgetName())
                .demandQuantity(infrastructureEntity.getDemandQuantity())
                .unit(infrastructureEntity.getUnit())
                .unitPriceExcludingTax(infrastructureEntity.getUnitPriceExcludingTax())
                .totalPriceExcludingTax(infrastructureEntity.getTotalPriceExcludingTax())
                .totalPriceIncludingTax(infrastructureEntity.getTotalPriceIncludingTax())
                .purchasedQuantity(infrastructureEntity.getPurchasedQuantity())
                .spentAmountExcludingTax(infrastructureEntity.getSpentAmountExcludingTax())
                .spentAmountIncludingTax(infrastructureEntity.getSpentAmountIncludingTax())
                .remainingAmountExcludingTax(infrastructureEntity.getRemainingAmountExcludingTax())
                .remainingAmountIncludingTax(infrastructureEntity.getRemainingAmountIncludingTax())
                .materialCode(infrastructureEntity.getMaterialCode())
                .materialName(infrastructureEntity.getMaterialName())
                .compilationBasis(infrastructureEntity.getCompilationBasis())
                .remark(infrastructureEntity.getRemark())
                .tenantId(infrastructureEntity.getTenantId())
                .createTime(infrastructureEntity.getCreateTime())
                .createBy(infrastructureEntity.getCreateBy())
                .updateTime(infrastructureEntity.getUpdateTime())
                .updateBy(infrastructureEntity.getUpdateBy())
                .delFlag(infrastructureEntity.getDelFlag())
                .sysOrgCode(infrastructureEntity.getSysOrgCode())
                .build();
    }

    /**
     * 批量转换（领域实体转基础设施实体）
     */
    public List<ProcurementExecutionBasis> toInfrastructureBatch(List<ProcurementExecutionBasisEntity> domainEntities) {
        if (domainEntities == null) {
            return null;
        }
        return domainEntities.stream()
                .map(this::toInfrastructure)
                .collect(Collectors.toList());
    }

    /**
     * 批量转换（基础设施实体转领域实体）
     */
    public List<ProcurementExecutionBasisEntity> toDomainBatch(List<ProcurementExecutionBasis> infrastructureEntities) {
        if (infrastructureEntities == null) {
            return null;
        }
        return infrastructureEntities.stream()
                .map(this::toDomain)
                .collect(Collectors.toList());
    }

    /**
     * 批量转换明细（领域实体转基础设施实体）
     */
    public List<ProcurementExecutionBasisDetail> toInfrastructureDetailBatch(List<ProcurementExecutionBasisDetailEntity> domainEntities) {
        if (domainEntities == null) {
            return null;
        }
        return domainEntities.stream()
                .map(this::toInfrastructureDetail)
                .collect(Collectors.toList());
    }

    /**
     * 批量转换明细（基础设施实体转领域实体）
     */
    public List<ProcurementExecutionBasisDetailEntity> toDomainDetailBatch(List<ProcurementExecutionBasisDetail> infrastructureEntities) {
        if (infrastructureEntities == null) {
            return null;
        }
        return infrastructureEntities.stream()
                .map(this::toDomainDetail)
                .collect(Collectors.toList());
    }
}
