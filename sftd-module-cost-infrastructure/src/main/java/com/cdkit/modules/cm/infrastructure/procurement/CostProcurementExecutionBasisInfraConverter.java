package com.cdkit.modules.cm.infrastructure.procurement;

import cn.hutool.core.bean.BeanUtil;
import com.cdkit.modules.cm.domain.procurement.entity.CostProcurementExecutionBasisEntity;
import com.cdkit.modules.cm.domain.procurement.entity.CostProcurementExecutionBasisDetailEntity;
import com.cdkit.modules.cm.infrastructure.procurement.entity.CostProcurementExecutionBasis;
import com.cdkit.modules.cm.infrastructure.procurement.entity.CostProcurementExecutionBasisDetail;

import java.util.List;

/**
 * 采购执行依据基础设施转换器
 * 
 * <AUTHOR>
 * @date 2025-08-25
 */
public class CostProcurementExecutionBasisInfraConverter {

    /**
     * 领域实体转基础设施实体（主表）
     */
    public static CostProcurementExecutionBasis convert(CostProcurementExecutionBasisEntity entity) {
        if (entity == null) {
            return null;
        }
        return BeanUtil.copyProperties(entity, CostProcurementExecutionBasis.class);
    }

    /**
     * 基础设施实体转领域实体（主表）
     */
    public static CostProcurementExecutionBasisEntity convertToDomain(CostProcurementExecutionBasis entity) {
        if (entity == null) {
            return null;
        }
        return BeanUtil.copyProperties(entity, CostProcurementExecutionBasisEntity.class);
    }

    /**
     * 领域实体转基础设施实体（明细表）
     */
    public static CostProcurementExecutionBasisDetail convertDetail(CostProcurementExecutionBasisDetailEntity entity) {
        if (entity == null) {
            return null;
        }
        return BeanUtil.copyProperties(entity, CostProcurementExecutionBasisDetail.class);
    }

    /**
     * 基础设施实体转领域实体（明细表）
     */
    public static CostProcurementExecutionBasisDetailEntity convertDetailToDomain(CostProcurementExecutionBasisDetail entity) {
        if (entity == null) {
            return null;
        }
        return BeanUtil.copyProperties(entity, CostProcurementExecutionBasisDetailEntity.class);
    }

    /**
     * 基础设施实体列表转领域实体列表（主表）
     */
    public static List<CostProcurementExecutionBasisEntity> convertList(List<CostProcurementExecutionBasis> entityList) {
        return BeanUtil.copyToList(entityList, CostProcurementExecutionBasisEntity.class);
    }

    /**
     * 领域实体列表转基础设施实体列表（主表）
     */
    public static List<CostProcurementExecutionBasis> convertToInfraList(List<CostProcurementExecutionBasisEntity> entityList) {
        return BeanUtil.copyToList(entityList, CostProcurementExecutionBasis.class);
    }

    /**
     * 基础设施实体列表转领域实体列表（明细表）
     */
    public static List<CostProcurementExecutionBasisDetailEntity> convertDetailList(List<CostProcurementExecutionBasisDetail> entityList) {
        return BeanUtil.copyToList(entityList, CostProcurementExecutionBasisDetailEntity.class);
    }

    /**
     * 领域实体列表转基础设施实体列表（明细表）
     */
    public static List<CostProcurementExecutionBasisDetail> convertDetailToInfraList(List<CostProcurementExecutionBasisDetailEntity> entityList) {
        return BeanUtil.copyToList(entityList, CostProcurementExecutionBasisDetail.class);
    }
}
