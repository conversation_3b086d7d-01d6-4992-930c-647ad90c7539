package com.cdkit.modules.cm.infrastructure.budget.service.impl;

import cn.hutool.core.bean.BeanUtil;
import com.cdkit.modules.cm.domain.budget.mode.entity.CostQuarterlyBudgetSubjectDirectCostEntity;
import com.cdkit.modules.cm.domain.budget.service.QuarterlyBudgetDetailQueryService;
import com.cdkit.modules.cm.infrastructure.budget.service.ICostQuarterlyBudgetProcPkgDetailService;
import com.cdkit.modules.cm.infrastructure.budget.service.ICostQuarterlyBudgetMaterialDetailService;
import com.cdkit.modules.cm.infrastructure.budget.service.ICostQuarterlyBudgetSubjectDirectCostService;
import com.cdkit.modules.cm.infrastructure.budget.service.ICostQuarterlyBudgetCenterIndirectCostService;
import com.cdkit.modules.cm.infrastructure.budget.service.ICostQuarterlyBudgetCompMageIndirectCostService;
import com.cdkit.modules.cm.infrastructure.budget.service.ICostQuarterlyBudgetNonOptCenterIndirectCostService;
import com.cdkit.modules.cm.infrastructure.budget.service.ICostQuarterlyBudgetRevenueDetailService;
import com.cdkit.modules.cm.infrastructure.budget.entity.CostQuarterlyBudgetProcPkgDetail;
import com.cdkit.modules.cm.infrastructure.budget.entity.CostQuarterlyBudgetMaterialDetail;
import com.cdkit.modules.cm.infrastructure.budget.entity.CostQuarterlyBudgetSubjectDirectCost;
import com.cdkit.modules.cm.infrastructure.budget.entity.CostQuarterlyBudgetCenterIndirectCost;
import com.cdkit.modules.cm.infrastructure.budget.entity.CostQuarterlyBudgetCompMageIndirectCost;
import com.cdkit.modules.cm.infrastructure.budget.entity.CostQuarterlyBudgetNonOptCenterIndirectCost;
import com.cdkit.modules.cm.infrastructure.budget.entity.CostQuarterlyBudgetRevenueDetail;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 季度预算详情查询服务实现
 * 
 * <AUTHOR>
 * @date 2025-08-20
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class QuarterlyBudgetDetailQueryServiceImpl implements QuarterlyBudgetDetailQueryService {

    private final ICostQuarterlyBudgetProcPkgDetailService costQuarterlyBudgetProcPkgDetailService;
    private final ICostQuarterlyBudgetMaterialDetailService costQuarterlyBudgetMaterialDetailService;
    private final ICostQuarterlyBudgetSubjectDirectCostService costQuarterlyBudgetSubjectDirectCostService;
    private final ICostQuarterlyBudgetCenterIndirectCostService costQuarterlyBudgetCenterIndirectCostService;
    private final ICostQuarterlyBudgetCompMageIndirectCostService costQuarterlyBudgetCompMageIndirectCostService;
    private final ICostQuarterlyBudgetNonOptCenterIndirectCostService costQuarterlyBudgetNonOptCenterIndirectCostService;
    private final ICostQuarterlyBudgetRevenueDetailService costQuarterlyBudgetRevenueDetailService;

    @Override
    public List<ProcPkgDetailInfo> queryProcPkgDetailByMainId(String quarterlyBudgetId) {
        log.info("开始根据季度预算ID查询采办包明细，quarterlyBudgetId: {}", quarterlyBudgetId);

        try {
            // 参数校验
            if (!StringUtils.hasText(quarterlyBudgetId)) {
                throw new IllegalArgumentException("季度预算ID不能为空");
            }

            // 调用infra层service查询
            List<CostQuarterlyBudgetProcPkgDetail> detailList = 
                costQuarterlyBudgetProcPkgDetailService.selectByMainId(quarterlyBudgetId);

            if (detailList == null) {
                detailList = new ArrayList<>();
            }

            // 转换为领域对象
            List<ProcPkgDetailInfo> result = detailList.stream()
                .map(this::convertToProcPkgDetailInfo)
                .collect(Collectors.toList());

            log.info("根据季度预算ID查询采办包明细成功，quarterlyBudgetId: {}, 查询到{}条记录",
                    quarterlyBudgetId, result.size());

            return result;

        } catch (Exception e) {
            log.error("根据季度预算ID查询采办包明细失败，quarterlyBudgetId: {}", quarterlyBudgetId, e);
            throw new RuntimeException("查询采办包明细失败：" + e.getMessage(), e);
        }
    }

    @Override
    public List<MaterialDetailInfo> queryMaterialDetailByMainId(String quarterlyBudgetId) {
        log.info("开始根据季度预算ID查询原材料明细，quarterlyBudgetId: {}", quarterlyBudgetId);

        try {
            // 参数校验
            if (!StringUtils.hasText(quarterlyBudgetId)) {
                throw new IllegalArgumentException("季度预算ID不能为空");
            }

            // 调用infra层service查询
            List<CostQuarterlyBudgetMaterialDetail> detailList = 
                costQuarterlyBudgetMaterialDetailService.selectByMainId(quarterlyBudgetId);

            if (detailList == null) {
                detailList = new ArrayList<>();
            }

            // 转换为领域对象
            List<MaterialDetailInfo> result = detailList.stream()
                .map(this::convertToMaterialDetailInfo)
                .collect(Collectors.toList());

            log.info("根据季度预算ID查询原材料明细成功，quarterlyBudgetId: {}, 查询到{}条记录",
                    quarterlyBudgetId, result.size());

            return result;

        } catch (Exception e) {
            log.error("根据季度预算ID查询原材料明细失败，quarterlyBudgetId: {}", quarterlyBudgetId, e);
            throw new RuntimeException("查询原材料明细失败：" + e.getMessage(), e);
        }
    }

    @Override
    public List<CostQuarterlyBudgetSubjectDirectCostEntity> querySubjectDirectCostByMainId(String quarterlyBudgetId) {
        log.info("开始根据季度预算ID查询预算科目明细直接成本，quarterlyBudgetId: {}", quarterlyBudgetId);

        try {
            // 参数校验
            if (!StringUtils.hasText(quarterlyBudgetId)) {
                throw new IllegalArgumentException("季度预算ID不能为空");
            }

            // 调用infra层service查询
            List<CostQuarterlyBudgetSubjectDirectCost> detailList =
                costQuarterlyBudgetSubjectDirectCostService.selectByMainId(quarterlyBudgetId);

            if (detailList == null) {
                detailList = new ArrayList<>();
            }

            // 转换为领域对象
            List<CostQuarterlyBudgetSubjectDirectCostEntity> result = BeanUtil.copyToList(detailList, CostQuarterlyBudgetSubjectDirectCostEntity.class);

            log.info("根据季度预算ID查询预算科目明细直接成本成功，quarterlyBudgetId: {}, 查询到{}条记录",
                    quarterlyBudgetId, result.size());

            return result;

        } catch (Exception e) {
            log.error("根据季度预算ID查询预算科目明细直接成本失败，quarterlyBudgetId: {}", quarterlyBudgetId, e);
            throw new RuntimeException("查询预算科目明细直接成本失败：" + e.getMessage(), e);
        }
    }

    @Override
    public List<CenterIndirectCostInfo> queryCenterIndirectCostByMainId(String quarterlyBudgetId) {
        log.info("开始根据季度预算ID查询本中心间接成本，quarterlyBudgetId: {}", quarterlyBudgetId);

        try {
            // 参数校验
            if (!StringUtils.hasText(quarterlyBudgetId)) {
                throw new IllegalArgumentException("季度预算ID不能为空");
            }

            // 调用infra层service查询
            List<CostQuarterlyBudgetCenterIndirectCost> detailList =
                costQuarterlyBudgetCenterIndirectCostService.selectByMainId(quarterlyBudgetId);

            if (detailList == null) {
                detailList = new ArrayList<>();
            }

            // 转换为领域对象
            List<CenterIndirectCostInfo> result = detailList.stream()
                .map(this::convertToCenterIndirectCostInfo)
                .collect(Collectors.toList());

            log.info("根据季度预算ID查询本中心间接成本成功，quarterlyBudgetId: {}, 查询到{}条记录",
                    quarterlyBudgetId, result.size());

            return result;

        } catch (Exception e) {
            log.error("根据季度预算ID查询本中心间接成本失败，quarterlyBudgetId: {}", quarterlyBudgetId, e);
            throw new RuntimeException("查询本中心间接成本失败：" + e.getMessage(), e);
        }
    }

    @Override
    public List<ComprehensiveIndirectCostInfo> queryCompMageIndirectCostByMainId(String quarterlyBudgetId) {
        log.info("开始根据季度预算ID查询综合管理间接成本，quarterlyBudgetId: {}", quarterlyBudgetId);

        try {
            // 参数校验
            if (!StringUtils.hasText(quarterlyBudgetId)) {
                throw new IllegalArgumentException("季度预算ID不能为空");
            }

            // 调用infra层service查询
            List<CostQuarterlyBudgetCompMageIndirectCost> detailList =
                costQuarterlyBudgetCompMageIndirectCostService.selectByMainId(quarterlyBudgetId);

            if (detailList == null) {
                detailList = new ArrayList<>();
            }

            // 转换为领域对象
            List<ComprehensiveIndirectCostInfo> result = detailList.stream()
                .map(this::convertToComprehensiveIndirectCostInfo)
                .collect(Collectors.toList());

            log.info("根据季度预算ID查询综合管理间接成本成功，quarterlyBudgetId: {}, 查询到{}条记录",
                    quarterlyBudgetId, result.size());

            return result;

        } catch (Exception e) {
            log.error("根据季度预算ID查询综合管理间接成本失败，quarterlyBudgetId: {}", quarterlyBudgetId, e);
            throw new RuntimeException("查询综合管理间接成本失败：" + e.getMessage(), e);
        }
    }

    @Override
    public List<NonOperatingIndirectCostInfo> queryNonOptCenterIndirectCostByMainId(String quarterlyBudgetId) {
        log.info("开始根据季度预算ID查询非经营中心间接成本，quarterlyBudgetId: {}", quarterlyBudgetId);

        try {
            // 参数校验
            if (!StringUtils.hasText(quarterlyBudgetId)) {
                throw new IllegalArgumentException("季度预算ID不能为空");
            }

            // 调用infra层service查询
            List<CostQuarterlyBudgetNonOptCenterIndirectCost> detailList =
                costQuarterlyBudgetNonOptCenterIndirectCostService.selectByMainId(quarterlyBudgetId);

            if (detailList == null) {
                detailList = new ArrayList<>();
            }

            // 转换为领域对象
            List<NonOperatingIndirectCostInfo> result = detailList.stream()
                .map(this::convertToNonOperatingIndirectCostInfo)
                .collect(Collectors.toList());

            log.info("根据季度预算ID查询非经营中心间接成本成功，quarterlyBudgetId: {}, 查询到{}条记录",
                    quarterlyBudgetId, result.size());

            return result;

        } catch (Exception e) {
            log.error("根据季度预算ID查询非经营中心间接成本失败，quarterlyBudgetId: {}", quarterlyBudgetId, e);
            throw new RuntimeException("查询非经营中心间接成本失败：" + e.getMessage(), e);
        }
    }

    @Override
    public List<RevenueDetailInfo> queryRevenueDetailByMainId(String quarterlyBudgetId) {
        log.info("开始根据季度预算ID查询收入明细，quarterlyBudgetId: {}", quarterlyBudgetId);

        try {
            // 参数校验
            if (!StringUtils.hasText(quarterlyBudgetId)) {
                throw new IllegalArgumentException("季度预算ID不能为空");
            }

            // 调用infra层service查询
            List<CostQuarterlyBudgetRevenueDetail> detailList = 
                costQuarterlyBudgetRevenueDetailService.selectByMainId(quarterlyBudgetId);

            if (detailList == null) {
                detailList = new ArrayList<>();
            }

            // 转换为领域对象
            List<RevenueDetailInfo> result = detailList.stream()
                .map(this::convertToRevenueDetailInfo)
                .collect(Collectors.toList());

            log.info("根据季度预算ID查询收入明细成功，quarterlyBudgetId: {}, 查询到{}条记录",
                    quarterlyBudgetId, result.size());

            return result;

        } catch (Exception e) {
            log.error("根据季度预算ID查询收入明细失败，quarterlyBudgetId: {}", quarterlyBudgetId, e);
            throw new RuntimeException("查询收入明细失败：" + e.getMessage(), e);
        }
    }

    // ==================== 转换方法 ====================

    /**
     * 转换采办包明细
     */
    private ProcPkgDetailInfo convertToProcPkgDetailInfo(CostQuarterlyBudgetProcPkgDetail entity) {
        ProcPkgDetailInfo info = new ProcPkgDetailInfo();
        BeanUtils.copyProperties(entity, info);
        return info;
    }

    /**
     * 转换原材料明细
     */
    private MaterialDetailInfo convertToMaterialDetailInfo(CostQuarterlyBudgetMaterialDetail entity) {
        MaterialDetailInfo info = new MaterialDetailInfo();
        BeanUtils.copyProperties(entity, info);
        return info;
    }

    /**
     * 转换预算科目明细直接成本
     */
    private SubjectDirectCostInfo convertToSubjectDirectCostInfo(CostQuarterlyBudgetSubjectDirectCost entity) {
        SubjectDirectCostInfo info = new SubjectDirectCostInfo();
        info.setBudgetSubjectCode(entity.getBudgetSubjectCode());
        info.setBudgetSubjectName(entity.getBudgetSubjectName());
        info.setSubjectDescription(entity.getSubjectDescription());
        info.setExpenditureBudgetAmount(entity.getExpenditureBudgetAmount());
        return info;
    }

    /**
     * 转换本中心间接成本
     */
    private CenterIndirectCostInfo convertToCenterIndirectCostInfo(CostQuarterlyBudgetCenterIndirectCost entity) {
        CenterIndirectCostInfo info = new CenterIndirectCostInfo();
        info.setBudgetSubjectCode(entity.getBudgetSubjectCode());
        info.setBudgetSubjectName(entity.getBudgetSubjectName());
        info.setSubjectDescription(entity.getSubjectDescription());
        info.setExpenditureBudgetAmount(entity.getExpenditureBudgetAmount());
        return info;
    }

    /**
     * 转换综合管理间接成本
     */
    private ComprehensiveIndirectCostInfo convertToComprehensiveIndirectCostInfo(CostQuarterlyBudgetCompMageIndirectCost entity) {
        ComprehensiveIndirectCostInfo info = new ComprehensiveIndirectCostInfo();
        info.setBudgetSubjectCode(entity.getBudgetSubjectCode());
        info.setBudgetSubjectName(entity.getBudgetSubjectName());
        info.setSubjectDescription(entity.getSubjectDescription());
        info.setExpenditureBudgetAmount(entity.getExpenditureBudgetAmount());
        return info;
    }

    /**
     * 转换非经营中心间接成本
     */
    private NonOperatingIndirectCostInfo convertToNonOperatingIndirectCostInfo(CostQuarterlyBudgetNonOptCenterIndirectCost entity) {
        NonOperatingIndirectCostInfo info = new NonOperatingIndirectCostInfo();
        info.setBudgetSubjectCode(entity.getBudgetSubjectCode());
        info.setBudgetSubjectName(entity.getBudgetSubjectName());
        info.setSubjectDescription(entity.getSubjectDescription());
        info.setExpenditureBudgetAmount(entity.getExpenditureBudgetAmount());
        return info;
    }

    /**
     * 转换收入明细
     */
    private RevenueDetailInfo convertToRevenueDetailInfo(CostQuarterlyBudgetRevenueDetail entity) {
        RevenueDetailInfo info = new RevenueDetailInfo();
        BeanUtils.copyProperties(entity, info);
        return info;
    }
}
