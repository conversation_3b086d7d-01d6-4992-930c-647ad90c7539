package com.cdkit.modules.cm.infrastructure.procurement.service.impl;


import com.cdkit.modules.cm.infrastructure.procurement.entity.CostProcurementApplication;
import com.cdkit.modules.cm.infrastructure.procurement.entity.CostProcurementApplicationDetail;
import com.cdkit.modules.cm.infrastructure.procurement.mapper.CostProcurementApplicationDetailMapper;
import com.cdkit.modules.cm.infrastructure.procurement.mapper.CostProcurementApplicationMapper;
import com.cdkit.modules.cm.infrastructure.procurement.service.ICostProcurementApplicationService;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;
import java.io.Serializable;
import java.util.List;
import java.util.Collection;

/**
 * @Description: 采购申请
 * @Author: cdkit-boot
 * @Date:   2025-08-26
 * @Version: V1.0
 */
@Service
public class CostProcurementApplicationServiceImpl extends ServiceImpl<CostProcurementApplicationMapper, CostProcurementApplication> implements ICostProcurementApplicationService {

	@Autowired
	private CostProcurementApplicationMapper costProcurementApplicationMapper;
	@Autowired
	private CostProcurementApplicationDetailMapper costProcurementApplicationDetailMapper;
	
	@Override
	@Transactional(rollbackFor = Exception.class)
	public void saveMain(CostProcurementApplication costProcurementApplication, List<CostProcurementApplicationDetail> costProcurementApplicationDetailList) {
		costProcurementApplication.setId(null);
		costProcurementApplicationMapper.insert(costProcurementApplication);
		if(costProcurementApplicationDetailList!=null && costProcurementApplicationDetailList.size()>0) {
			for(CostProcurementApplicationDetail entity:costProcurementApplicationDetailList) {
				//外键设置
				entity.setId(null);
				entity.setApplicationId(costProcurementApplication.getId());
				costProcurementApplicationDetailMapper.insert(entity);
			}
		}
	}

	@Override
	@Transactional(rollbackFor = Exception.class)
	public void updateMain(CostProcurementApplication costProcurementApplication,List<CostProcurementApplicationDetail> costProcurementApplicationDetailList) {
		costProcurementApplicationMapper.updateById(costProcurementApplication);
		
		//1.先删除子表数据
		costProcurementApplicationDetailMapper.deleteByMainId(costProcurementApplication.getId());
		
		//2.子表数据重新插入
		if(costProcurementApplicationDetailList!=null && costProcurementApplicationDetailList.size()>0) {
			for(CostProcurementApplicationDetail entity:costProcurementApplicationDetailList) {
				//外键设置
				entity.setId(null);
				entity.setApplicationId(costProcurementApplication.getId());
				costProcurementApplicationDetailMapper.insert(entity);
			}
		}
	}

	@Override
	@Transactional(rollbackFor = Exception.class)
	public void delMain(String id) {
		costProcurementApplicationDetailMapper.deleteByMainId(id);
		costProcurementApplicationMapper.deleteById(id);
	}

	@Override
	@Transactional(rollbackFor = Exception.class)
	public void delBatchMain(Collection<? extends Serializable> idList) {
		for(Serializable id:idList) {
			costProcurementApplicationDetailMapper.deleteByMainId(id.toString());
			costProcurementApplicationMapper.deleteById(id);
		}
	}
	
}
