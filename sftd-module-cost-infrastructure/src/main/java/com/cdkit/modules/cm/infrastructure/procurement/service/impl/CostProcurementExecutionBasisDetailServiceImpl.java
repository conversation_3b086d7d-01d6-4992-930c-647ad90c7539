package com.cdkit.modules.cm.infrastructure.procurement.service.impl;


import com.cdkit.modules.cm.infrastructure.procurement.entity.CostProcurementExecutionBasisDetail;
import com.cdkit.modules.cm.infrastructure.procurement.mapper.CostProcurementExecutionBasisDetailMapper;
import com.cdkit.modules.cm.infrastructure.procurement.service.ICostProcurementExecutionBasisDetailService;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;
import java.util.List;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.beans.factory.annotation.Autowired;

/**
 * @Description: 采购执行依据明细表
 * @Author: sunhzh
 * @Date:   2025-08-21
 * @Version: V1.0
 */
@Service
public class CostProcurementExecutionBasisDetailServiceImpl extends ServiceImpl<CostProcurementExecutionBasisDetailMapper, CostProcurementExecutionBasisDetail> implements ICostProcurementExecutionBasisDetailService {
	
	@Resource
	private CostProcurementExecutionBasisDetailMapper costProcurementExecutionBasisDetailMapper;
	
	@Override
	public List<CostProcurementExecutionBasisDetail> selectByMainId(String mainId) {
		return costProcurementExecutionBasisDetailMapper.selectByMainId(mainId);
	}
}
