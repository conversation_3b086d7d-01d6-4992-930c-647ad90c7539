package com.cdkit.modules.cm.infrastructure.procurement.mapper;

import java.util.List;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.cdkit.modules.cm.infrastructure.procurement.entity.CostProcurementApplicationDetail;
import org.apache.ibatis.annotations.Param;

/**
 * @Description: 采购申请明细信息
 * @Author: cdkit-boot
 * @Date:   2025-08-26
 * @Version: V1.0
 */
public interface CostProcurementApplicationDetailMapper extends BaseMapper<CostProcurementApplicationDetail> {

	/**
	 * 通过主表id删除子表数据
	 *
	 * @param mainId 主表id
	 * @return boolean
	 */
	public boolean deleteByMainId(@Param("mainId") String mainId);

  /**
   * 通过主表id查询子表数据
   *
   * @param mainId 主表id
   * @return List<CostProcurementApplicationDetail>
   */
	public List<CostProcurementApplicationDetail> selectByMainId(@Param("mainId") String mainId);
}
