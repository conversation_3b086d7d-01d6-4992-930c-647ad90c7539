package com.cdkit.modules.cm.infrastructure.procurement.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.cdkit.modules.cm.infrastructure.procurement.po.ProcurementExecutionBasis;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.time.LocalDate;
import java.util.List;

/**
 * 采购执行依据主表Mapper
 * 
 * <AUTHOR>
 * @date 2025-08-21
 */
@Mapper
public interface ProcurementExecutionBasisMapper extends BaseMapper<ProcurementExecutionBasis> {

    /**
     * 根据物料编码和时间周期查询执行依据
     */
    @Select("""
        SELECT * FROM cost_procurement_execution_basis 
        WHERE material_code = #{materialCode} 
          AND start_date = #{startDate} 
          AND end_date = #{endDate} 
          AND tenant_id = #{tenantId} 
          AND del_flag = 0
        """)
    ProcurementExecutionBasis findByMaterialAndPeriod(@Param("materialCode") String materialCode,
                                                      @Param("startDate") LocalDate startDate,
                                                      @Param("endDate") LocalDate endDate,
                                                      @Param("tenantId") Integer tenantId);

    /**
     * 统计生成结果 - 主表数量
     */
    @Select("""
        SELECT COUNT(DISTINCT peb.id) 
        FROM cost_procurement_execution_basis peb
        INNER JOIN cost_procurement_execution_basis_detail pebd ON peb.id = pebd.execution_basis_id
        WHERE pebd.quarterly_budget_id = #{quarterlyBudgetId} 
          AND peb.tenant_id = #{tenantId} 
          AND peb.del_flag = 0
        """)
    Integer countMainRecordsByBudgetId(@Param("quarterlyBudgetId") String quarterlyBudgetId,
                                      @Param("tenantId") Integer tenantId);

    /**
     * 统计物料数量
     */
    @Select("""
        SELECT COUNT(DISTINCT peb.material_code) 
        FROM cost_procurement_execution_basis peb
        INNER JOIN cost_procurement_execution_basis_detail pebd ON peb.id = pebd.execution_basis_id
        WHERE pebd.quarterly_budget_id = #{quarterlyBudgetId} 
          AND peb.tenant_id = #{tenantId} 
          AND peb.del_flag = 0
        """)
    Integer countMaterialsByBudgetId(@Param("quarterlyBudgetId") String quarterlyBudgetId,
                                    @Param("tenantId") Integer tenantId);
}
