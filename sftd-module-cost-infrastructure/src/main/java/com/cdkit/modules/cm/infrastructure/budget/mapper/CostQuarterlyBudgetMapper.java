package com.cdkit.modules.cm.infrastructure.budget.mapper;

import java.util.List;
import java.math.BigDecimal;

import com.cdkit.modules.cm.domain.procurement.mode.valobj.MaterialDemandData;
import com.cdkit.modules.cm.infrastructure.budget.entity.CostQuarterlyBudget;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;

/**
 * @Description: 季度预算主表
 * @Author: cdkit-boot
 * @Date:   2025-08-12
 * @Version: V1.0
 */
public interface CostQuarterlyBudgetMapper extends BaseMapper<CostQuarterlyBudget> {

    /**
     * 汇总与指定年度预算相关的已审批通过的季度预算的项目预算总额
     * 用于计算年度收入剩余预算金额
     *
     * @param annualBudgetId 年度预算ID
     * @param excludeQuarterlyBudgetId 排除的季度预算ID（可为null）
     * @return 已审批季度预算的项目预算总额
     */
    BigDecimal sumApprovedQuarterlyBudgetRevenueByAnnualBudgetId(
            @Param("annualBudgetId") String annualBudgetId,
            @Param("excludeQuarterlyBudgetId") String excludeQuarterlyBudgetId);

    /**
     * 汇总与指定年度预算和预算科目相关的已审批通过的季度预算的支出金额
     * 用于计算年度剩余支出预算金额
     *
     * @param annualBudgetId 年度预算ID
     * @param budgetSubjectCode 预算科目编码
     * @param excludeQuarterlyBudgetId 排除的季度预算ID（可为null）
     * @return 已审批季度预算的支出金额
     */
    BigDecimal sumApprovedQuarterlyBudgetExpenditureBySubjectCode(
            @Param("annualBudgetId") String annualBudgetId,
            @Param("budgetSubjectCode") String budgetSubjectCode,
            @Param("excludeQuarterlyBudgetId") String excludeQuarterlyBudgetId);

    /**
     * 查询物料需求数据
     * 用于生成采购执行依据
     */
    @Select("""
        SELECT
            qb.id as quarterlyBudgetId,
            qb.quarterly_budget_no as quarterlyBudgetNo,
            qb.quarterly_budget_name as quarterlyBudgetName,
            qb.quarter as quarter,
            qb.start_date as startDate,
            qb.end_date as endDate,
            md.material_code as materialCode,
            md.material_name as materialName,
            md.unit as unit,
            md.usage_amount as usageAmount,
            md.unit_price_excluding_tax as unitPriceExcludingTax,
            md.total_price_excluding_tax as totalPriceExcludingTax,
            md.compilation_basis as compilationBasis,
            md.remark as remark,
            qb.tenant_id as tenantId
        FROM cost_quarterly_budget qb
        INNER JOIN cost_quarterly_budget_material_detail md ON qb.id = md.quarterly_budget_id
        WHERE qb.budget_status = 'LOCKED'
          AND qb.tenant_id = #{tenantId}
          AND qb.del_flag = 0
          AND md.del_flag = 0
          AND (#{quarterlyBudgetId} IS NULL OR qb.id = #{quarterlyBudgetId})
        ORDER BY qb.start_date, qb.end_date, md.material_code
        """)
    List<MaterialDemandData> findMaterialDemandsByBudgetId(@Param("quarterlyBudgetId") String quarterlyBudgetId,
                                                          @Param("tenantId") Integer tenantId);

}
