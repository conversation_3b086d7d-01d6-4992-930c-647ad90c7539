package com.cdkit.modules.cm.infrastructure.procurement.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.cdkit.modules.cm.infrastructure.procurement.entity.CostProcurementExecutionBasisDetail;

import java.util.List;

/**
 * @Description: 采购执行依据明细表
 * @Author: sunhzh
 * @Date:   2025-08-21
 * @Version: V1.0
 */
public interface ICostProcurementExecutionBasisDetailService extends IService<CostProcurementExecutionBasisDetail> {

	/**
	 * 通过主表id查询子表数据
	 *
	 * @param mainId 主表id
	 * @return List<CostProcurementExecutionBasisDetail>
	 */
	public List<CostProcurementExecutionBasisDetail> selectByMainId(String mainId);
}
