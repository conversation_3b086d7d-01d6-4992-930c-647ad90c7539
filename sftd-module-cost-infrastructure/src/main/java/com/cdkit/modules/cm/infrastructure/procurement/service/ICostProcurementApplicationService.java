package com.cdkit.modules.cm.infrastructure.procurement.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.cdkit.modules.cm.infrastructure.procurement.entity.CostProcurementApplication;
import com.cdkit.modules.cm.infrastructure.procurement.entity.CostProcurementApplicationDetail;

import java.io.Serializable;
import java.util.Collection;
import java.util.List;

/**
 * @Description: 采购申请
 * @Author: cdkit-boot
 * @Date:   2025-08-26
 * @Version: V1.0
 */
public interface ICostProcurementApplicationService extends IService<CostProcurementApplication> {

	/**
	 * 添加一对多
	 *
	 * @param costProcurementApplication
	 * @param costProcurementApplicationDetailList
	 */
	public void saveMain(CostProcurementApplication costProcurementApplication,List<CostProcurementApplicationDetail> costProcurementApplicationDetailList) ;
	
	/**
	 * 修改一对多
	 *
   * @param costProcurementApplication
   * @param costProcurementApplicationDetailList
	 */
	public void updateMain(CostProcurementApplication costProcurementApplication,List<CostProcurementApplicationDetail> costProcurementApplicationDetailList);
	
	/**
	 * 删除一对多
	 *
	 * @param id
	 */
	public void delMain (String id);
	
	/**
	 * 批量删除一对多
	 *
	 * @param idList
	 */
	public void delBatchMain (Collection<? extends Serializable> idList);
	
}
