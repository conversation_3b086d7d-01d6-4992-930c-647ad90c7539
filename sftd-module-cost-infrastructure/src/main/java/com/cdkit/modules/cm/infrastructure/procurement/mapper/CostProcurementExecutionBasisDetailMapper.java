package com.cdkit.modules.cm.infrastructure.procurement.mapper;

import java.util.List;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.cdkit.modules.cm.infrastructure.procurement.entity.CostProcurementExecutionBasisDetail;
import org.apache.ibatis.annotations.Param;

/**
 * @Description: 采购执行依据明细表
 * @Author: sunhzh
 * @Date:   2025-08-21
 * @Version: V1.0
 */
public interface CostProcurementExecutionBasisDetailMapper extends BaseMapper<CostProcurementExecutionBasisDetail> {

	/**
	 * 通过主表id删除子表数据
	 *
	 * @param mainId 主表id
	 * @return boolean
	 */
	public boolean deleteByMainId(@Param("mainId") String mainId);

  /**
   * 通过主表id查询子表数据
   *
   * @param mainId 主表id
   * @return List<CostProcurementExecutionBasisDetail>
   */
	public List<CostProcurementExecutionBasisDetail> selectByMainId(@Param("mainId") String mainId);
}
