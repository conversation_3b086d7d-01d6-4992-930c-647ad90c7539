package com.cdkit.modules.cm.infrastructure.procurement.repository;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.cdkit.modules.cm.domain.procurement.mode.entity.ProcurementExecutionBasisDetailEntity;
import com.cdkit.modules.cm.domain.procurement.mode.entity.ProcurementExecutionBasisEntity;
import com.cdkit.modules.cm.domain.procurement.repository.ProcurementExecutionBasisRepository;
import com.cdkit.modules.cm.infrastructure.procurement.converter.ProcurementExecutionBasisConverter;
import com.cdkit.modules.cm.infrastructure.procurement.mapper.ProcurementExecutionBasisDetailMapper;
import com.cdkit.modules.cm.infrastructure.procurement.mapper.ProcurementExecutionBasisMapper;
import com.cdkit.modules.cm.infrastructure.procurement.po.ProcurementExecutionBasis;
import com.cdkit.modules.cm.infrastructure.procurement.po.ProcurementExecutionBasisDetail;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Repository;

import java.time.LocalDate;
import java.util.List;

/**
 * 采购执行依据仓储实现
 * 
 * <AUTHOR>
 * @date 2025-08-21
 */
@Repository
@RequiredArgsConstructor
@Slf4j
public class ProcurementExecutionBasisRepositoryImpl implements ProcurementExecutionBasisRepository {

    private final ProcurementExecutionBasisMapper procurementExecutionBasisMapper;
    private final ProcurementExecutionBasisDetailMapper procurementExecutionBasisDetailMapper;
    private final ProcurementExecutionBasisConverter procurementExecutionBasisConverter;

    @Override
    public ProcurementExecutionBasisEntity save(ProcurementExecutionBasisEntity entity) {
        ProcurementExecutionBasis infraEntity = procurementExecutionBasisConverter.toInfrastructure(entity);
        procurementExecutionBasisMapper.insert(infraEntity);
        return procurementExecutionBasisConverter.toDomain(infraEntity);
    }

    @Override
    public List<ProcurementExecutionBasisEntity> saveBatch(List<ProcurementExecutionBasisEntity> entities) {
        if (entities == null || entities.isEmpty()) {
            return entities;
        }

        List<ProcurementExecutionBasis> infraEntities = procurementExecutionBasisConverter.toInfrastructureBatch(entities);
        
        // 批量插入
        for (ProcurementExecutionBasis infraEntity : infraEntities) {
            procurementExecutionBasisMapper.insert(infraEntity);
        }

        return procurementExecutionBasisConverter.toDomainBatch(infraEntities);
    }

    @Override
    public ProcurementExecutionBasisDetailEntity saveDetail(ProcurementExecutionBasisDetailEntity entity) {
        ProcurementExecutionBasisDetail infraEntity = procurementExecutionBasisConverter.toInfrastructureDetail(entity);
        procurementExecutionBasisDetailMapper.insert(infraEntity);
        return procurementExecutionBasisConverter.toDomainDetail(infraEntity);
    }

    @Override
    public List<ProcurementExecutionBasisDetailEntity> saveDetailBatch(List<ProcurementExecutionBasisDetailEntity> entities) {
        if (entities == null || entities.isEmpty()) {
            return entities;
        }

        List<ProcurementExecutionBasisDetail> infraEntities = procurementExecutionBasisConverter.toInfrastructureDetailBatch(entities);
        
        // 批量插入
        for (ProcurementExecutionBasisDetail infraEntity : infraEntities) {
            procurementExecutionBasisDetailMapper.insert(infraEntity);
        }

        return procurementExecutionBasisConverter.toDomainDetailBatch(infraEntities);
    }

    @Override
    public ProcurementExecutionBasisEntity findByMaterialAndPeriod(String materialCode, LocalDate startDate, 
                                                                  LocalDate endDate, Integer tenantId) {
        ProcurementExecutionBasis infraEntity = procurementExecutionBasisMapper.findByMaterialAndPeriod(
                materialCode, startDate, endDate, tenantId);
        return procurementExecutionBasisConverter.toDomain(infraEntity);
    }

    @Override
    public void deleteByQuarterlyBudgetId(String quarterlyBudgetId, Integer tenantId) {
        // 删除明细表数据
        LambdaUpdateWrapper<ProcurementExecutionBasisDetail> detailUpdateWrapper = new LambdaUpdateWrapper<>();
        detailUpdateWrapper.eq(ProcurementExecutionBasisDetail::getQuarterlyBudgetId, quarterlyBudgetId)
                .eq(ProcurementExecutionBasisDetail::getTenantId, tenantId)
                .set(ProcurementExecutionBasisDetail::getDelFlag, 1);
        
        int detailDeleteCount = procurementExecutionBasisDetailMapper.update(null, detailUpdateWrapper);

        // 查找没有有效明细的主表记录并删除
        List<ProcurementExecutionBasis> mainRecordsToDelete = procurementExecutionBasisMapper.selectList(
                new LambdaQueryWrapper<ProcurementExecutionBasis>()
                        .eq(ProcurementExecutionBasis::getTenantId, tenantId)
                        .eq(ProcurementExecutionBasis::getDelFlag, 0)
                        .notExists("SELECT 1 FROM cost_procurement_execution_basis_detail pebd " +
                                  "WHERE pebd.execution_basis_id = cost_procurement_execution_basis.id " +
                                  "AND pebd.del_flag = 0")
        );

        if (!mainRecordsToDelete.isEmpty()) {
            List<String> mainIdsToDelete = mainRecordsToDelete.stream()
                    .map(ProcurementExecutionBasis::getId)
                    .toList();

            LambdaUpdateWrapper<ProcurementExecutionBasis> mainUpdateWrapper = new LambdaUpdateWrapper<>();
            mainUpdateWrapper.in(ProcurementExecutionBasis::getId, mainIdsToDelete)
                    .set(ProcurementExecutionBasis::getDelFlag, 1);
            
            int mainDeleteCount = procurementExecutionBasisMapper.update(null, mainUpdateWrapper);
            log.info("删除采购执行依据数据完成，明细: {}条, 主表: {}条", detailDeleteCount, mainDeleteCount);
        } else {
            log.info("删除采购执行依据数据完成，明细: {}条, 主表: 0条", detailDeleteCount);
        }
    }

    @Override
    public List<ProcurementExecutionBasisDetailEntity> findDetailsByExecutionBasisId(String executionBasisId) {
        LambdaQueryWrapper<ProcurementExecutionBasisDetail> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ProcurementExecutionBasisDetail::getExecutionBasisId, executionBasisId)
                .eq(ProcurementExecutionBasisDetail::getDelFlag, 0);
        
        List<ProcurementExecutionBasisDetail> infraEntities = procurementExecutionBasisDetailMapper.selectList(queryWrapper);
        return procurementExecutionBasisConverter.toDomainDetailBatch(infraEntities);
    }

    @Override
    public int[] countGenerationResult(String quarterlyBudgetId, Integer tenantId) {
        Integer mainCount = procurementExecutionBasisMapper.countMainRecordsByBudgetId(quarterlyBudgetId, tenantId);
        Integer detailCount = procurementExecutionBasisDetailMapper.countDetailsByBudgetId(quarterlyBudgetId, tenantId);
        Integer materialCount = procurementExecutionBasisMapper.countMaterialsByBudgetId(quarterlyBudgetId, tenantId);
        
        return new int[]{
                mainCount != null ? mainCount : 0,
                detailCount != null ? detailCount : 0,
                materialCount != null ? materialCount : 0
        };
    }

    @Override
    public List<ProcurementExecutionBasisEntity> findByTenantId(Integer tenantId) {
        LambdaQueryWrapper<ProcurementExecutionBasis> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ProcurementExecutionBasis::getTenantId, tenantId)
                .eq(ProcurementExecutionBasis::getDelFlag, 0);
        
        List<ProcurementExecutionBasis> infraEntities = procurementExecutionBasisMapper.selectList(queryWrapper);
        return procurementExecutionBasisConverter.toDomainBatch(infraEntities);
    }

    @Override
    public void updatePurchasedAmountStatistics(String materialCode, LocalDate startDate, 
                                              LocalDate endDate, Integer tenantId) {
        // TODO: 实现已采购量统计更新逻辑
        // 这里需要根据实际的采购申请表结构来实现
        log.info("更新已采购量统计，物料: {}, 时间周期: {} - {}, 租户: {}", 
                materialCode, startDate, endDate, tenantId);
    }
}
