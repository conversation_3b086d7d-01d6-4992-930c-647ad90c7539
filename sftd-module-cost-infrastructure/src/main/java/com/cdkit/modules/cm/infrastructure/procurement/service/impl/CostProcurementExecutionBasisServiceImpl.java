package com.cdkit.modules.cm.infrastructure.procurement.service.impl;


import com.cdkit.modules.cm.infrastructure.procurement.entity.CostProcurementExecutionBasis;
import com.cdkit.modules.cm.infrastructure.procurement.entity.CostProcurementExecutionBasisDetail;
import com.cdkit.modules.cm.infrastructure.procurement.mapper.CostProcurementExecutionBasisDetailMapper;
import com.cdkit.modules.cm.infrastructure.procurement.mapper.CostProcurementExecutionBasisMapper;
import com.cdkit.modules.cm.infrastructure.procurement.service.ICostProcurementExecutionBasisService;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;
import java.io.Serializable;
import java.util.List;
import java.util.Collection;

/**
 * @Description: 采购执行依据主表
 * @Author: sunhzh
 * @Date:   2025-08-21
 * @Version: V1.0
 */
@Service
public class CostProcurementExecutionBasisServiceImpl extends ServiceImpl<CostProcurementExecutionBasisMapper, CostProcurementExecutionBasis> implements ICostProcurementExecutionBasisService {

	@Resource
	private CostProcurementExecutionBasisMapper costProcurementExecutionBasisMapper;
	@Resource
	private CostProcurementExecutionBasisDetailMapper costProcurementExecutionBasisDetailMapper;
	
	@Override
	@Transactional(rollbackFor = Exception.class)
	public void saveMain(CostProcurementExecutionBasis costProcurementExecutionBasis, List<CostProcurementExecutionBasisDetail> costProcurementExecutionBasisDetailList) {
		costProcurementExecutionBasisMapper.insert(costProcurementExecutionBasis);
		if(costProcurementExecutionBasisDetailList!=null && costProcurementExecutionBasisDetailList.size()>0) {
			for(CostProcurementExecutionBasisDetail entity:costProcurementExecutionBasisDetailList) {
				//外键设置
				entity.setExecutionBasisId(costProcurementExecutionBasis.getId());
				costProcurementExecutionBasisDetailMapper.insert(entity);
			}
		}
	}

	@Override
	@Transactional(rollbackFor = Exception.class)
	public void updateMain(CostProcurementExecutionBasis costProcurementExecutionBasis,List<CostProcurementExecutionBasisDetail> costProcurementExecutionBasisDetailList) {
		costProcurementExecutionBasisMapper.updateById(costProcurementExecutionBasis);
		
		//1.先删除子表数据
		costProcurementExecutionBasisDetailMapper.deleteByMainId(costProcurementExecutionBasis.getId());
		
		//2.子表数据重新插入
		if(costProcurementExecutionBasisDetailList!=null && costProcurementExecutionBasisDetailList.size()>0) {
			for(CostProcurementExecutionBasisDetail entity:costProcurementExecutionBasisDetailList) {
				//外键设置
				entity.setExecutionBasisId(costProcurementExecutionBasis.getId());
				costProcurementExecutionBasisDetailMapper.insert(entity);
			}
		}
	}

	@Override
	@Transactional(rollbackFor = Exception.class)
	public void delMain(String id) {
		costProcurementExecutionBasisDetailMapper.deleteByMainId(id);
		costProcurementExecutionBasisMapper.deleteById(id);
	}

	@Override
	@Transactional(rollbackFor = Exception.class)
	public void delBatchMain(Collection<? extends Serializable> idList) {
		for(Serializable id:idList) {
			costProcurementExecutionBasisDetailMapper.deleteByMainId(id.toString());
			costProcurementExecutionBasisMapper.deleteById(id);
		}
	}
	
}
