<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cdkit.modules.cm.infrastructure.procurement.mapper.CostProcurementApplicationDetailMapper">

	<delete id="deleteByMainId" parameterType="java.lang.String">
		UPDATE  cost_procurement_application_detail  set del_flag=1
		WHERE
			 application_id = #{mainId}</delete>
	
	<select id="selectByMainId" parameterType="java.lang.String" resultType="com.cdkit.modules.cm.infrastructure.procurement.entity.CostProcurementApplicationDetail">
		SELECT * 
		FROM  cost_procurement_application_detail
		WHERE
			 application_id = #{mainId}  AND del_flag=0	</select>
</mapper>
