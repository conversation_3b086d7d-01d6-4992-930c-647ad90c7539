package com.cdkit.modules.cm.infrastructure.procurement.po;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * 采购执行依据主表基础设施实体
 * 
 * <AUTHOR>
 * @date 2025-08-21
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@TableName("cost_procurement_execution_basis")
public class ProcurementExecutionBasis {

    /**
     * 主键ID
     */
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private String id;

    /**
     * 物料编码
     */
    @TableField("material_code")
    private String materialCode;

    /**
     * 物料名称
     */
    @TableField("material_name")
    private String materialName;

    /**
     * 计量单位
     */
    @TableField("unit")
    private String unit;

    /**
     * 所在季度
     */
    @TableField("quarter")
    private String quarter;

    /**
     * 开始时间
     */
    @TableField("start_date")
    private LocalDate startDate;

    /**
     * 结束时间
     */
    @TableField("end_date")
    private LocalDate endDate;

    /**
     * 总需求量
     */
    @TableField("total_demand_quantity")
    private BigDecimal totalDemandQuantity;

    /**
     * 不含税单价
     */
    @TableField("unit_price_excluding_tax")
    private BigDecimal unitPriceExcludingTax;

    /**
     * 不含税总价
     */
    @TableField("total_price_excluding_tax")
    private BigDecimal totalPriceExcludingTax;

    /**
     * 含税总价
     */
    @TableField("total_price_including_tax")
    private BigDecimal totalPriceIncludingTax;

    /**
     * 已采购量
     */
    @TableField("purchased_quantity")
    private BigDecimal purchasedQuantity;

    /**
     * 已支出不含税金额
     */
    @TableField("spent_amount_excluding_tax")
    private BigDecimal spentAmountExcludingTax;

    /**
     * 已支出含税金额
     */
    @TableField("spent_amount_including_tax")
    private BigDecimal spentAmountIncludingTax;

    /**
     * 剩余不含税金额
     */
    @TableField("remaining_amount_excluding_tax")
    private BigDecimal remainingAmountExcludingTax;

    /**
     * 剩余含税金额
     */
    @TableField("remaining_amount_including_tax")
    private BigDecimal remainingAmountIncludingTax;

    /**
     * 生成状态
     */
    @TableField("generation_status")
    private String generationStatus;

    /**
     * 来源季度预算数量
     */
    @TableField("source_quarterly_budget_count")
    private Integer sourceQuarterlyBudgetCount;

    /**
     * 租户ID
     */
    @TableField("tenant_id")
    private Integer tenantId;

    /**
     * 创建时间
     */
    @TableField("create_time")
    private LocalDateTime createTime;

    /**
     * 创建人
     */
    @TableField("create_by")
    private String createBy;

    /**
     * 更新时间
     */
    @TableField("update_time")
    private LocalDateTime updateTime;

    /**
     * 更新人
     */
    @TableField("update_by")
    private String updateBy;

    /**
     * 删除标识
     */
    @TableField("del_flag")
    private Integer delFlag;

    /**
     * 所属部门代码
     */
    @TableField("sys_org_code")
    private String sysOrgCode;
}
