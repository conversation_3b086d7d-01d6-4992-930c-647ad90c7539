package com.cdkit.modules.cm.infrastructure.project.repository;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.OrderItem;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.cdkit.common.config.TenantContext;
import com.cdkit.common.exception.CdkitCloudException;
import com.cdkit.common.page.PageReq;
import com.cdkit.common.page.PageRes;
import com.cdkit.common.system.query.QueryGenerator;
import com.cdkit.common.util.TenantIgnoreContext;
import com.cdkit.modules.cm.domain.project.mode.entity.CostProjectEntity;
import com.cdkit.modules.cm.domain.project.mode.entity.CostProjectWorkloadEntity;
import com.cdkit.modules.cm.domain.project.repository.CostProjectRepository;
import com.cdkit.modules.cm.domain.project.valobj.ProjectStageEnum;
import com.cdkit.modules.cm.infrastructure.project.CostProjectInfraConverter;
import com.cdkit.modules.cm.infrastructure.project.CostProjectWorkloadInfraConverter;
import com.cdkit.modules.cm.infrastructure.project.entity.CostProject;
import com.cdkit.modules.cm.infrastructure.project.entity.CostProjectWorkload;
import com.cdkit.modules.cm.infrastructure.project.service.ICostProjectService;
import com.cdkit.modules.cm.infrastructure.project.service.ICostProjectWorkloadService;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 市场项目台账仓储实现
 * <AUTHOR>
 * @date 2025/07/14
 */
@Repository
@RequiredArgsConstructor
public class CostProjectRepositoryImpl implements CostProjectRepository {

    private final ICostProjectService costProjectService;
    private final ICostProjectWorkloadService costProjectWorkloadService;

    @Override
    public PageRes<CostProjectEntity> page(CostProjectEntity queryEntity, PageReq pageReq) {
        CostProject convert = CostProjectInfraConverter.convert(queryEntity);

        LambdaQueryWrapper<CostProject> queryWrapper = new LambdaQueryWrapper<>();
        // 添加查询条件
        queryWrapper.like(StrUtil.isNotEmpty(convert.getProjectName()), CostProject::getProjectName, convert.getProjectName());
        queryWrapper.like(StrUtil.isNotEmpty(convert.getProjectCode()), CostProject::getProjectCode, convert.getProjectCode());
        queryWrapper.eq(StrUtil.isNotEmpty(convert.getProjectStatus()), CostProject::getProjectStatus, convert.getProjectStatus());
        queryWrapper.like(StrUtil.isNotEmpty(convert.getCustomerName()), CostProject::getCustomerName, convert.getCustomerName());
        queryWrapper.like(StrUtil.isNotEmpty(convert.getProjectLeader()), CostProject::getProjectLeader, convert.getProjectLeader());

        // 添加排序逻辑
        if (pageReq.getOrderParam() != null && !pageReq.getOrderParam().isEmpty()) {
            pageReq.getOrderParam().forEach(orderParam -> {
                if ("desc".equalsIgnoreCase(orderParam.getOrder())) {
                    if ("create_time".equals(orderParam.getField())) {
                        queryWrapper.orderByDesc(CostProject::getCreateTime);
                    }
                } else {
                    if ("create_time".equals(orderParam.getField())) {
                        queryWrapper.orderByAsc(CostProject::getCreateTime);
                    }
                }
            });
        } else {
            // 默认按创建时间降序
            queryWrapper.orderByDesc(CostProject::getCreateTime);
        }

        Page<CostProject> page = new Page<>(pageReq.getCurrent(), pageReq.getSize());

        Page<CostProject> pageList = costProjectService.page(page, queryWrapper);
        List<CostProject> records = pageList.getRecords();
        List<CostProjectEntity> projectEntities = CostProjectInfraConverter.convertList(records);

        return PageRes.of(pageReq.getCurrent(), pageReq.getSize(), projectEntities, pageList.getTotal(), pageList.getPages());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean saveMain(CostProjectEntity costProject, List<CostProjectWorkloadEntity> workloadList) {
        try {
            CostProject project = CostProjectInfraConverter.convert(costProject);
            List<CostProjectWorkload> workloads = new ArrayList<>();
            
            if (workloadList != null && !workloadList.isEmpty()) {
                workloads = workloadList.stream()
                        .map(CostProjectWorkloadInfraConverter::convert)
                        .collect(Collectors.toList());
            }
            project.setProjectStage(ProjectStageEnum.PENDING_CONTRACT.getCode());
            costProjectService.saveMain(project, workloads);
            
            // 设置返回的ID
            costProject.setId(project.getId());
            return true;
        } catch (Exception e) {
            throw new CdkitCloudException("保存项目数据失败"+e.getMessage());
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateMain(CostProjectEntity costProject, List<CostProjectWorkloadEntity> workloadList) {
        try {
            CostProject project = CostProjectInfraConverter.convert(costProject);
            List<CostProjectWorkload> workloads = new ArrayList<>();
            
            if (workloadList != null && !workloadList.isEmpty()) {
                workloads = workloadList.stream()
                        .map(CostProjectWorkloadInfraConverter::convert)
                        .collect(Collectors.toList());
            }
            
            costProjectService.updateMain(project, workloads);
            return true;
        } catch (Exception e) {
            throw new CdkitCloudException("更新项目数据失败"+ e.getMessage());
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean deleteMain(String id) {
        try {
            costProjectService.delMain(id);
            return true;
        } catch (Exception e) {
            throw new CdkitCloudException("删除项目数据失败"+e.getMessage());
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean deleteBatchMain(List<String> ids) {
        try {
            costProjectService.delBatchMain(ids);
            return true;
        } catch (Exception e) {
            throw new CdkitCloudException("批量删除项目数据失败"+e.getMessage());
        }
    }

    @Override
    public List<CostProjectWorkloadEntity> queryWorkloadByProjectId(String projectId) {
        List<CostProjectWorkload> workloads = costProjectWorkloadService.selectByMainId(projectId);
        return CostProjectWorkloadInfraConverter.convertList(workloads);
    }

    @Override
    public CostProjectEntity queryByIdWithWorkload(String id) {
        CostProject project = costProjectService.getById(id);
        if (project == null) {
            return null;
        }
        
        CostProjectEntity projectEntity = CostProjectInfraConverter.convert(project);
        List<CostProjectWorkloadEntity> workloadList = queryWorkloadByProjectId(id);
        projectEntity.setCostProjectWorkloadList(workloadList);
        
        return projectEntity;
    }

    // 实现IBaseDomainRepository的基础方法
    @Override
    public String addDomain(CostProjectEntity entity) {
        CostProject project = CostProjectInfraConverter.convert(entity);
        costProjectService.save(project);
        return project.getId();
    }

    @Override
    public void deleteDomainById(String id) {
        costProjectService.removeById(id);
    }

    @Override
    public void deleteDomainById(List<String> ids) {
        costProjectService.removeByIds(ids);
    }

    @Override
    public CostProjectEntity updateDomainById(CostProjectEntity entity) {
        CostProject project = CostProjectInfraConverter.convert(entity);
        costProjectService.updateById(project);
        return CostProjectInfraConverter.convert(project);
    }

    @Override
    public CostProjectEntity getDomainById(String id) {
        CostProject project = costProjectService.getById(id);
        return project != null ? CostProjectInfraConverter.convert(project) : null;
    }

    @Override
    public List<CostProjectEntity> queryInExecutionProjectsByYear(String projectYear) {
        LambdaQueryWrapper<CostProject> queryWrapper = new LambdaQueryWrapper<>();
        // 添加查询条件：项目年度和项目阶段为执行中
        queryWrapper.eq(CostProject::getProjectYear, projectYear);
        queryWrapper.eq(CostProject::getProjectStage, ProjectStageEnum.IN_EXECUTION.getCode());

        List<CostProject> projects = costProjectService.list(queryWrapper);
        return CostProjectInfraConverter.convertList(projects);
    }
}
