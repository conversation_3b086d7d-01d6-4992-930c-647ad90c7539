package com.cdkit.modules.cm.infrastructure.procurement.service.impl;


import com.cdkit.modules.cm.infrastructure.procurement.entity.CostProcurementApplicationDetail;
import com.cdkit.modules.cm.infrastructure.procurement.mapper.CostProcurementApplicationDetailMapper;
import com.cdkit.modules.cm.infrastructure.procurement.service.ICostProcurementApplicationDetailService;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;
import java.util.List;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.beans.factory.annotation.Autowired;

/**
 * @Description: 采购申请明细信息
 * @Author: cdkit-boot
 * @Date:   2025-08-26
 * @Version: V1.0
 */
@Service
public class CostProcurementApplicationDetailServiceImpl extends ServiceImpl<CostProcurementApplicationDetailMapper, CostProcurementApplicationDetail> implements ICostProcurementApplicationDetailService {
	
	@Resource
	private CostProcurementApplicationDetailMapper costProcurementApplicationDetailMapper;
	
	@Override
	public List<CostProcurementApplicationDetail> selectByMainId(String mainId) {
		return costProcurementApplicationDetailMapper.selectByMainId(mainId);
	}
}
