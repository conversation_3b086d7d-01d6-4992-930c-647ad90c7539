package com.cdkit.modules.cm.infrastructure.procurement.po;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 采购执行依据明细表基础设施实体
 * 
 * <AUTHOR>
 * @date 2025-08-21
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@TableName("cost_procurement_execution_basis_detail")
public class ProcurementExecutionBasisDetail {

    /**
     * 主键ID
     */
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private String id;

    /**
     * 关联采购执行依据主表ID
     */
    @TableField("execution_basis_id")
    private String executionBasisId;

    /**
     * 关联季度预算主表ID
     */
    @TableField("quarterly_budget_id")
    private String quarterlyBudgetId;

    /**
     * 预算编号
     */
    @TableField("quarterly_budget_no")
    private String quarterlyBudgetNo;

    /**
     * 预算名称
     */
    @TableField("quarterly_budget_name")
    private String quarterlyBudgetName;

    /**
     * 需求量
     */
    @TableField("demand_quantity")
    private BigDecimal demandQuantity;

    /**
     * 计量单位
     */
    @TableField("unit")
    private String unit;

    /**
     * 不含税单价
     */
    @TableField("unit_price_excluding_tax")
    private BigDecimal unitPriceExcludingTax;

    /**
     * 不含税总价
     */
    @TableField("total_price_excluding_tax")
    private BigDecimal totalPriceExcludingTax;

    /**
     * 含税总价
     */
    @TableField("total_price_including_tax")
    private BigDecimal totalPriceIncludingTax;

    /**
     * 已采购量
     */
    @TableField("purchased_quantity")
    private BigDecimal purchasedQuantity;

    /**
     * 已支出不含税金额
     */
    @TableField("spent_amount_excluding_tax")
    private BigDecimal spentAmountExcludingTax;

    /**
     * 已支出含税金额
     */
    @TableField("spent_amount_including_tax")
    private BigDecimal spentAmountIncludingTax;

    /**
     * 剩余不含税金额
     */
    @TableField("remaining_amount_excluding_tax")
    private BigDecimal remainingAmountExcludingTax;

    /**
     * 剩余含税金额
     */
    @TableField("remaining_amount_including_tax")
    private BigDecimal remainingAmountIncludingTax;

    /**
     * 物料编码
     */
    @TableField("material_code")
    private String materialCode;

    /**
     * 物料名称
     */
    @TableField("material_name")
    private String materialName;

    /**
     * 编制依据
     */
    @TableField("compilation_basis")
    private String compilationBasis;

    /**
     * 备注
     */
    @TableField("remark")
    private String remark;

    /**
     * 租户ID
     */
    @TableField("tenant_id")
    private Integer tenantId;

    /**
     * 创建时间
     */
    @TableField("create_time")
    private LocalDateTime createTime;

    /**
     * 创建人
     */
    @TableField("create_by")
    private String createBy;

    /**
     * 更新时间
     */
    @TableField("update_time")
    private LocalDateTime updateTime;

    /**
     * 更新人
     */
    @TableField("update_by")
    private String updateBy;

    /**
     * 删除标识
     */
    @TableField("del_flag")
    private Integer delFlag;

    /**
     * 所属部门代码
     */
    @TableField("sys_org_code")
    private String sysOrgCode;
}
