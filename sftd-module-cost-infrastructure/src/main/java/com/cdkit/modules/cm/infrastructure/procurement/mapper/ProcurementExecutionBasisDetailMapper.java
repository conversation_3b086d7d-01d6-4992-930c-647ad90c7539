package com.cdkit.modules.cm.infrastructure.procurement.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.cdkit.modules.cm.infrastructure.procurement.po.ProcurementExecutionBasisDetail;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

/**
 * 采购执行依据明细表Mapper
 * 
 * <AUTHOR>
 * @date 2025-08-21
 */
@Mapper
public interface ProcurementExecutionBasisDetailMapper extends BaseMapper<ProcurementExecutionBasisDetail> {

    /**
     * 统计明细数量
     */
    @Select("""
        SELECT COUNT(*) 
        FROM cost_procurement_execution_basis_detail 
        WHERE quarterly_budget_id = #{quarterlyBudgetId} 
          AND tenant_id = #{tenantId} 
          AND del_flag = 0
        """)
    Integer countDetailsByBudgetId(@Param("quarterlyBudgetId") String quarterlyBudgetId,
                                  @Param("tenantId") Integer tenantId);
}
