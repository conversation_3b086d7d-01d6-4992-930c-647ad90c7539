package com.cdkit.modules.cm.infrastructure.procurement.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.cdkit.modules.cm.infrastructure.procurement.entity.CostProcurementApplicationDetail;

import java.util.List;

/**
 * @Description: 采购申请明细信息
 * @Author: cdkit-boot
 * @Date:   2025-08-26
 * @Version: V1.0
 */
public interface ICostProcurementApplicationDetailService extends IService<CostProcurementApplicationDetail> {

	/**
	 * 通过主表id查询子表数据
	 *
	 * @param mainId 主表id
	 * @return List<CostProcurementApplicationDetail>
	 */
	public List<CostProcurementApplicationDetail> selectByMainId(String mainId);
}
