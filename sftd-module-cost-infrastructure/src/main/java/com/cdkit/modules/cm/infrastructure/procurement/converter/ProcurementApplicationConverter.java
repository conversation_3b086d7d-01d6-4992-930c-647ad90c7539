package com.cdkit.modules.cm.infrastructure.procurement.converter;

import com.cdkit.modules.cm.domain.procurement.entity.ProcurementApplicationEntity;
import com.cdkit.modules.cm.domain.procurement.entity.ProcurementApplicationDetailEntity;
import com.cdkit.modules.cm.infrastructure.procurement.entity.CostProcurementApplication;
import com.cdkit.modules.cm.infrastructure.procurement.entity.CostProcurementApplicationDetail;

import java.util.List;
import java.util.stream.Collectors;

/**
 * 采购申请转换器
 * 
 * <AUTHOR>
 * @date 2025-08-26
 */
public class ProcurementApplicationConverter {

    /**
     * 领域实体转基础设施实体
     */
    public static CostProcurementApplication toInfrastructure(ProcurementApplicationEntity domain) {
        if (domain == null) {
            return null;
        }
        
        CostProcurementApplication infra = new CostProcurementApplication();
        infra.setId(domain.getId());
        infra.setApplicationNo(domain.getApplicationNo());
        infra.setMaterialCode(domain.getMaterialCode());
        infra.setMaterialName(domain.getMaterialName());
        infra.setUnit(domain.getUnit());
        infra.setQuarter(domain.getQuarter());
        infra.setStartDate(domain.getStartDate());
        infra.setEndDate(domain.getEndDate());
        infra.setBudgetTotalQuantity(domain.getBudgetTotalQuantity());
        infra.setPurchasedQuantity(domain.getPurchasedQuantity());
        infra.setAvailableQuantity(domain.getAvailableQuantity());
        infra.setCurrentPurchaseQuantity(domain.getCurrentPurchaseQuantity());
        infra.setUnitPriceExcludingTax(domain.getUnitPriceExcludingTax());
        infra.setTotalPriceExcludingTax(domain.getTotalPriceExcludingTax());
        infra.setTotalPriceIncludingTax(domain.getTotalPriceIncludingTax());
        infra.setTaxAmount(domain.getTaxAmount());
        infra.setExecutionBasisId(domain.getExecutionBasisId());
        infra.setCreateTime(domain.getCreateTime());
        infra.setCreateBy(domain.getCreateBy());
        infra.setUpdateTime(domain.getUpdateTime());
        infra.setUpdateBy(domain.getUpdateBy());
        infra.setTenantId(domain.getTenantId());
        infra.setDelFlag(domain.getDelFlag());
        infra.setSysOrgCode(domain.getSysOrgCode());
        
        return infra;
    }

    /**
     * 基础设施实体转领域实体
     */
    public static ProcurementApplicationEntity toDomain(CostProcurementApplication infra) {
        if (infra == null) {
            return null;
        }
        
        return ProcurementApplicationEntity.builder()
                .id(infra.getId())
                .applicationNo(infra.getApplicationNo())
                .materialCode(infra.getMaterialCode())
                .materialName(infra.getMaterialName())
                .unit(infra.getUnit())
                .quarter(infra.getQuarter())
                .startDate(infra.getStartDate())
                .endDate(infra.getEndDate())
                .budgetTotalQuantity(infra.getBudgetTotalQuantity())
                .purchasedQuantity(infra.getPurchasedQuantity())
                .availableQuantity(infra.getAvailableQuantity())
                .currentPurchaseQuantity(infra.getCurrentPurchaseQuantity())
                .unitPriceExcludingTax(infra.getUnitPriceExcludingTax())
                .totalPriceExcludingTax(infra.getTotalPriceExcludingTax())
                .totalPriceIncludingTax(infra.getTotalPriceIncludingTax())
                .taxAmount(infra.getTaxAmount())
                .executionBasisId(infra.getExecutionBasisId())
                .createTime(infra.getCreateTime())
                .createBy(infra.getCreateBy())
                .updateTime(infra.getUpdateTime())
                .updateBy(infra.getUpdateBy())
                .tenantId(infra.getTenantId())
                .delFlag(infra.getDelFlag())
                .sysOrgCode(infra.getSysOrgCode())
                .build();
    }

    /**
     * 明细领域实体转基础设施实体
     */
    public static CostProcurementApplicationDetail toInfrastructureDetail(ProcurementApplicationDetailEntity domain) {
        if (domain == null) {
            return null;
        }
        
        CostProcurementApplicationDetail infra = new CostProcurementApplicationDetail();
        infra.setId(domain.getId());
        infra.setApplicationId(domain.getApplicationId());
        infra.setBudgetCode(domain.getBudgetCode());
        infra.setBudgetName(domain.getBudgetName());
        infra.setBudgetTotalQuantity(domain.getBudgetTotalQuantity());
        infra.setAvailableQuantity(domain.getAvailableQuantity());
        infra.setCurrentPurchaseQuantity(domain.getCurrentPurchaseQuantity());
        infra.setUnitPriceExcludingTax(domain.getUnitPriceExcludingTax());
        infra.setTotalPriceExcludingTax(domain.getTotalPriceExcludingTax());
        infra.setTotalPriceIncludingTax(domain.getTotalPriceIncludingTax());
        infra.setTaxAmount(domain.getTaxAmount());
        infra.setCreateTime(domain.getCreateTime());
        infra.setCreateBy(domain.getCreateBy());
        infra.setUpdateTime(domain.getUpdateTime());
        infra.setUpdateBy(domain.getUpdateBy());
        infra.setTenantId(domain.getTenantId());
        infra.setDelFlag(domain.getDelFlag());
        infra.setSysOrgCode(domain.getSysOrgCode());
        
        return infra;
    }

    /**
     * 明细基础设施实体转领域实体
     */
    public static ProcurementApplicationDetailEntity toDomainDetail(CostProcurementApplicationDetail infra) {
        if (infra == null) {
            return null;
        }
        
        return ProcurementApplicationDetailEntity.builder()
                .id(infra.getId())
                .applicationId(infra.getApplicationId())
                .budgetCode(infra.getBudgetCode())
                .budgetName(infra.getBudgetName())
                .budgetTotalQuantity(infra.getBudgetTotalQuantity())
                .availableQuantity(infra.getAvailableQuantity())
                .currentPurchaseQuantity(infra.getCurrentPurchaseQuantity())
                .unitPriceExcludingTax(infra.getUnitPriceExcludingTax())
                .totalPriceExcludingTax(infra.getTotalPriceExcludingTax())
                .totalPriceIncludingTax(infra.getTotalPriceIncludingTax())
                .taxAmount(infra.getTaxAmount())
                .createTime(infra.getCreateTime())
                .createBy(infra.getCreateBy())
                .updateTime(infra.getUpdateTime())
                .updateBy(infra.getUpdateBy())
                .tenantId(infra.getTenantId())
                .delFlag(infra.getDelFlag())
                .sysOrgCode(infra.getSysOrgCode())
                .build();
    }

    /**
     * 批量转换领域实体列表
     */
    public static List<ProcurementApplicationEntity> toDomainList(List<CostProcurementApplication> infraList) {
        if (infraList == null) {
            return null;
        }
        return infraList.stream()
                .map(ProcurementApplicationConverter::toDomain)
                .collect(Collectors.toList());
    }

    /**
     * 批量转换明细领域实体列表
     */
    public static List<ProcurementApplicationDetailEntity> toDomainDetailList(List<CostProcurementApplicationDetail> infraList) {
        if (infraList == null) {
            return null;
        }
        return infraList.stream()
                .map(ProcurementApplicationConverter::toDomainDetail)
                .collect(Collectors.toList());
    }
}
