package com.cdkit.modules.cm.infrastructure.procurement.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.cdkit.modules.cm.infrastructure.procurement.entity.CostProcurementExecutionBasis;
import com.cdkit.modules.cm.infrastructure.procurement.entity.CostProcurementExecutionBasisDetail;

import java.io.Serializable;
import java.util.Collection;
import java.util.List;

/**
 * @Description: 采购执行依据主表
 * @Author: sunhzh
 * @Date:   2025-08-21
 * @Version: V1.0
 */
public interface ICostProcurementExecutionBasisService extends IService<CostProcurementExecutionBasis> {

	/**
	 * 添加一对多
	 *
	 * @param costProcurementExecutionBasis
	 * @param costProcurementExecutionBasisDetailList
	 */
	public void saveMain(CostProcurementExecutionBasis costProcurementExecutionBasis,List<CostProcurementExecutionBasisDetail> costProcurementExecutionBasisDetailList) ;
	
	/**
	 * 修改一对多
	 *
   * @param costProcurementExecutionBasis
   * @param costProcurementExecutionBasisDetailList
	 */
	public void updateMain(CostProcurementExecutionBasis costProcurementExecutionBasis,List<CostProcurementExecutionBasisDetail> costProcurementExecutionBasisDetailList);
	
	/**
	 * 删除一对多
	 *
	 * @param id
	 */
	public void delMain (String id);
	
	/**
	 * 批量删除一对多
	 *
	 * @param idList
	 */
	public void delBatchMain (Collection<? extends Serializable> idList);
	
}
