package com.cdkit.config;

import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;

import java.util.concurrent.Executor;
import java.util.concurrent.ThreadPoolExecutor;

/**
 * 异步配置类
 * 配置异步任务执行器，用于事件监听器的异步处理
 * 
 * <AUTHOR>
 * @date 2025-08-21
 */
@Configuration
@EnableAsync
@Slf4j
public class AsyncConfig {

    /**
     * 配置异步任务执行器
     * 用于处理领域事件的异步监听器
     * 
     * @return 异步任务执行器
     */
    @Bean("taskExecutor")
    public Executor taskExecutor() {
        ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();
        
        // 核心线程数
        executor.setCorePoolSize(5);
        
        // 最大线程数
        executor.setMaxPoolSize(20);
        
        // 队列容量
        executor.setQueueCapacity(200);
        
        // 线程名前缀
        executor.setThreadNamePrefix("async-event-");
        
        // 线程空闲时间（秒）
        executor.setKeepAliveSeconds(60);
        
        // 拒绝策略：由调用线程处理该任务
        executor.setRejectedExecutionHandler(new ThreadPoolExecutor.CallerRunsPolicy());
        
        // 等待所有任务结束后再关闭线程池
        executor.setWaitForTasksToCompleteOnShutdown(true);
        
        // 等待时间（秒）
        executor.setAwaitTerminationSeconds(60);
        
        // 异常处理器
        executor.setTaskDecorator(runnable -> {
            return () -> {
                try {
                    runnable.run();
                } catch (Exception e) {
                    log.error("异步任务执行异常", e);
                }
            };
        });
        
        executor.initialize();
        
        log.info("异步任务执行器初始化完成 - 核心线程数: {}, 最大线程数: {}, 队列容量: {}", 
                executor.getCorePoolSize(), executor.getMaxPoolSize(), executor.getQueueCapacity());
        
        return executor;
    }

    /**
     * 配置事件处理专用的异步执行器
     * 专门用于处理领域事件，与通用异步任务分离
     * 
     * @return 事件处理异步执行器
     */
    @Bean("eventTaskExecutor")
    public Executor eventTaskExecutor() {
        ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();
        
        // 核心线程数（事件处理通常不需要太多线程）
        executor.setCorePoolSize(3);
        
        // 最大线程数
        executor.setMaxPoolSize(10);
        
        // 队列容量
        executor.setQueueCapacity(100);
        
        // 线程名前缀
        executor.setThreadNamePrefix("event-handler-");
        
        // 线程空闲时间（秒）
        executor.setKeepAliveSeconds(30);
        
        // 拒绝策略：由调用线程处理该任务（确保事件不丢失）
        executor.setRejectedExecutionHandler(new ThreadPoolExecutor.CallerRunsPolicy());
        
        // 等待所有任务结束后再关闭线程池
        executor.setWaitForTasksToCompleteOnShutdown(true);
        
        // 等待时间（秒）
        executor.setAwaitTerminationSeconds(30);
        
        // 异常处理器
        executor.setTaskDecorator(runnable -> {
            return () -> {
                try {
                    runnable.run();
                } catch (Exception e) {
                    log.error("事件处理异步任务执行异常", e);
                    // 事件处理异常不应该影响系统运行，只记录日志
                }
            };
        });
        
        executor.initialize();
        
        log.info("事件处理异步执行器初始化完成 - 核心线程数: {}, 最大线程数: {}, 队列容量: {}", 
                executor.getCorePoolSize(), executor.getMaxPoolSize(), executor.getQueueCapacity());
        
        return executor;
    }
}
