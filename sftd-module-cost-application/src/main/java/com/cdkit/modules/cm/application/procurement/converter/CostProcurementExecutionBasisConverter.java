package com.cdkit.modules.cm.application.procurement.converter;

import com.cdkit.modules.cm.api.procurement.dto.CostProcurementExecutionBasisDTO;
import com.cdkit.modules.cm.api.procurement.dto.CostProcurementExecutionBasisDetailDTO;
import com.cdkit.modules.cm.api.procurement.dto.CostProcurementExecutionBasisQueryDTO;
import com.cdkit.modules.cm.domain.procurement.entity.CostProcurementExecutionBasisEntity;
import com.cdkit.modules.cm.domain.procurement.entity.CostProcurementExecutionBasisDetailEntity;
import org.springframework.beans.BeanUtils;

import java.util.List;
import java.util.stream.Collectors;

/**
 * 采购执行依据应用层转换器
 * 
 * <AUTHOR>
 * @date 2025-08-25
 */
public class CostProcurementExecutionBasisConverter {

    /**
     * 查询DTO转领域实体
     */
    public static CostProcurementExecutionBasisEntity toEntity(CostProcurementExecutionBasisQueryDTO queryDTO) {
        if (queryDTO == null) {
            return null;
        }
        CostProcurementExecutionBasisEntity entity = new CostProcurementExecutionBasisEntity();
        BeanUtils.copyProperties(queryDTO, entity);
        return entity;
    }

    /**
     * 领域实体转DTO
     */
    public static CostProcurementExecutionBasisDTO toDTO(CostProcurementExecutionBasisEntity entity) {
        if (entity == null) {
            return null;
        }
        CostProcurementExecutionBasisDTO dto = new CostProcurementExecutionBasisDTO();
        BeanUtils.copyProperties(entity, dto);
        
        // 转换明细列表
        if (entity.getCostProcurementExecutionBasisDetailList() != null) {
            List<CostProcurementExecutionBasisDetailDTO> detailDTOList = entity.getCostProcurementExecutionBasisDetailList()
                    .stream()
                    .map(CostProcurementExecutionBasisConverter::toDetailDTO)
                    .collect(Collectors.toList());
            dto.setDetails(detailDTOList);
        }
        
        return dto;
    }

    /**
     * DTO转领域实体
     */
    public static CostProcurementExecutionBasisEntity toEntity(CostProcurementExecutionBasisDTO dto) {
        if (dto == null) {
            return null;
        }
        CostProcurementExecutionBasisEntity entity = new CostProcurementExecutionBasisEntity();
        BeanUtils.copyProperties(dto, entity);
        
        // 转换明细列表
        if (dto.getDetails() != null) {
            List<CostProcurementExecutionBasisDetailEntity> detailEntityList = dto.getDetails()
                    .stream()
                    .map(CostProcurementExecutionBasisConverter::toDetailEntity)
                    .collect(Collectors.toList());
            entity.setCostProcurementExecutionBasisDetailList(detailEntityList);
        }
        
        return entity;
    }

    /**
     * 明细领域实体转DTO
     */
    public static CostProcurementExecutionBasisDetailDTO toDetailDTO(CostProcurementExecutionBasisDetailEntity entity) {
        if (entity == null) {
            return null;
        }
        CostProcurementExecutionBasisDetailDTO dto = new CostProcurementExecutionBasisDetailDTO();
        BeanUtils.copyProperties(entity, dto);
        return dto;
    }

    /**
     * 明细DTO转领域实体
     */
    public static CostProcurementExecutionBasisDetailEntity toDetailEntity(CostProcurementExecutionBasisDetailDTO dto) {
        if (dto == null) {
            return null;
        }
        CostProcurementExecutionBasisDetailEntity entity = new CostProcurementExecutionBasisDetailEntity();
        BeanUtils.copyProperties(dto, entity);
        return entity;
    }

    /**
     * 领域实体列表转DTO列表
     */
    public static List<CostProcurementExecutionBasisDTO> toDTOList(List<CostProcurementExecutionBasisEntity> entityList) {
        if (entityList == null) {
            return null;
        }
        return entityList.stream()
                .map(CostProcurementExecutionBasisConverter::toDTO)
                .collect(Collectors.toList());
    }

    /**
     * DTO列表转领域实体列表
     */
    public static List<CostProcurementExecutionBasisEntity> toEntityList(List<CostProcurementExecutionBasisDTO> dtoList) {
        if (dtoList == null) {
            return null;
        }
        return dtoList.stream()
                .map(CostProcurementExecutionBasisConverter::toEntity)
                .collect(Collectors.toList());
    }

    /**
     * 明细领域实体列表转DTO列表
     */
    public static List<CostProcurementExecutionBasisDetailDTO> toDetailDTOList(List<CostProcurementExecutionBasisDetailEntity> entityList) {
        if (entityList == null) {
            return null;
        }
        return entityList.stream()
                .map(CostProcurementExecutionBasisConverter::toDetailDTO)
                .collect(Collectors.toList());
    }

    /**
     * 明细DTO列表转领域实体列表
     */
    public static List<CostProcurementExecutionBasisDetailEntity> toDetailEntityList(List<CostProcurementExecutionBasisDetailDTO> dtoList) {
        if (dtoList == null) {
            return null;
        }
        return dtoList.stream()
                .map(CostProcurementExecutionBasisConverter::toDetailEntity)
                .collect(Collectors.toList());
    }
}
