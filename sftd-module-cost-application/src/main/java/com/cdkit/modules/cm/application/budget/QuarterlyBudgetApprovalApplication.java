package com.cdkit.modules.cm.application.budget;

import com.cdkit.modules.cm.domain.budget.service.QuarterlyBudgetStatusUpdateService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import java.util.List;

/**
 * 季度预算审批应用服务
 * 处理季度预算的审批相关业务逻辑
 * 
 * <AUTHOR>
 * @date 2025-08-21
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class QuarterlyBudgetApprovalApplication {

    private final QuarterlyBudgetStatusUpdateService quarterlyBudgetStatusUpdateService;

    /**
     * 审批通过季度预算
     * 将预算状态更新为LOCKED，并自动触发采购执行依据生成
     * 
     * @param budgetIds 预算ID列表
     * @param operatorBy 操作人
     * @return 审批结果
     */
    @Transactional(rollbackFor = Exception.class)
    public ApprovalResult approveBudgets(List<String> budgetIds, String operatorBy) {
        log.info("开始审批通过季度预算，预算数量: {}, 操作人: {}", budgetIds.size(), operatorBy);

        // 参数校验
        if (budgetIds == null || budgetIds.isEmpty()) {
            String errorMsg = "预算ID列表不能为空";
            log.error(errorMsg);
            return ApprovalResult.failure(errorMsg);
        }

        if (!StringUtils.hasText(operatorBy)) {
            String errorMsg = "操作人不能为空";
            log.error(errorMsg);
            return ApprovalResult.failure(errorMsg);
        }

        try {
            long startTime = System.currentTimeMillis();

            // 调用领域服务更新状态为LOCKED，会自动发布锁定事件
            quarterlyBudgetStatusUpdateService.approveBudgets(budgetIds, operatorBy);

            long processingTime = System.currentTimeMillis() - startTime;

            log.info("审批通过季度预算成功，预算数量: {}, 耗时: {}ms", budgetIds.size(), processingTime);
            return ApprovalResult.success(budgetIds.size(), processingTime);

        } catch (Exception e) {
            log.error("审批通过季度预算失败，预算数量: {}", budgetIds.size(), e);
            return ApprovalResult.failure("审批失败: " + e.getMessage());
        }
    }

    /**
     * 审批驳回季度预算
     * 将预算状态更新为REJECTED
     * 
     * @param budgetIds 预算ID列表
     * @param operatorBy 操作人
     * @param rejectReason 驳回原因
     * @return 审批结果
     */
    @Transactional(rollbackFor = Exception.class)
    public ApprovalResult rejectBudgets(List<String> budgetIds, String operatorBy, String rejectReason) {
        log.info("开始审批驳回季度预算，预算数量: {}, 操作人: {}, 驳回原因: {}", 
                budgetIds.size(), operatorBy, rejectReason);

        // 参数校验
        if (budgetIds == null || budgetIds.isEmpty()) {
            String errorMsg = "预算ID列表不能为空";
            log.error(errorMsg);
            return ApprovalResult.failure(errorMsg);
        }

        if (!StringUtils.hasText(operatorBy)) {
            String errorMsg = "操作人不能为空";
            log.error(errorMsg);
            return ApprovalResult.failure(errorMsg);
        }

        if (!StringUtils.hasText(rejectReason)) {
            String errorMsg = "驳回原因不能为空";
            log.error(errorMsg);
            return ApprovalResult.failure(errorMsg);
        }

        try {
            long startTime = System.currentTimeMillis();

            // 调用领域服务更新状态为REJECTED
            quarterlyBudgetStatusUpdateService.rejectBudgets(budgetIds, operatorBy, rejectReason);

            long processingTime = System.currentTimeMillis() - startTime;

            log.info("审批驳回季度预算成功，预算数量: {}, 耗时: {}ms", budgetIds.size(), processingTime);
            return ApprovalResult.success(budgetIds.size(), processingTime);

        } catch (Exception e) {
            log.error("审批驳回季度预算失败，预算数量: {}", budgetIds.size(), e);
            return ApprovalResult.failure("驳回失败: " + e.getMessage());
        }
    }

    /**
     * 单个预算审批通过
     * 
     * @param budgetId 预算ID
     * @param operatorBy 操作人
     * @return 审批结果
     */
    @Transactional(rollbackFor = Exception.class)
    public ApprovalResult approveBudget(String budgetId, String operatorBy) {
        return approveBudgets(List.of(budgetId), operatorBy);
    }

    /**
     * 单个预算审批驳回
     * 
     * @param budgetId 预算ID
     * @param operatorBy 操作人
     * @param rejectReason 驳回原因
     * @return 审批结果
     */
    @Transactional(rollbackFor = Exception.class)
    public ApprovalResult rejectBudget(String budgetId, String operatorBy, String rejectReason) {
        return rejectBudgets(List.of(budgetId), operatorBy, rejectReason);
    }

    /**
     * 审批结果
     */
    public static class ApprovalResult {
        /**
         * 是否成功
         */
        private final boolean success;

        /**
         * 处理数量
         */
        private final int processedCount;

        /**
         * 错误信息
         */
        private final String errorMessage;

        /**
         * 处理耗时（毫秒）
         */
        private final long processingTimeMs;

        private ApprovalResult(boolean success, int processedCount, String errorMessage, long processingTimeMs) {
            this.success = success;
            this.processedCount = processedCount;
            this.errorMessage = errorMessage;
            this.processingTimeMs = processingTimeMs;
        }

        public static ApprovalResult success(int processedCount, long processingTimeMs) {
            return new ApprovalResult(true, processedCount, null, processingTimeMs);
        }

        public static ApprovalResult failure(String errorMessage) {
            return new ApprovalResult(false, 0, errorMessage, 0);
        }

        // Getters
        public boolean isSuccess() { return success; }
        public int getProcessedCount() { return processedCount; }
        public String getErrorMessage() { return errorMessage; }
        public long getProcessingTimeMs() { return processingTimeMs; }

        @Override
        public String toString() {
            if (success) {
                return String.format("审批成功: 处理%d条记录, 耗时%dms", processedCount, processingTimeMs);
            } else {
                return String.format("审批失败: %s", errorMessage);
            }
        }
    }
}
