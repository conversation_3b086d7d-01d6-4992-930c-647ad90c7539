package com.cdkit.modules.cm.application.procurement;

import com.cdkit.common.page.OrderParam;
import com.cdkit.common.page.PageReq;
import com.cdkit.common.page.PageRes;
import com.cdkit.modules.cm.domain.procurement.entity.ProcurementApplicationEntity;
import com.cdkit.modules.cm.domain.procurement.repository.ProcurementApplicationRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;

/**
 * 采购申请应用服务
 * 
 * <AUTHOR>
 * @date 2025-08-26
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class ProcurementApplicationService {

    private final ProcurementApplicationRepository procurementApplicationRepository;

    /**
     * 分页查询采购申请列表
     * 按照创建时间倒序排列
     *
     * @param queryEntity 查询条件
     * @param pageNo 页码
     * @param pageSize 每页数量
     * @return 分页结果
     */
    public PageRes<ProcurementApplicationEntity> queryPageList(ProcurementApplicationEntity queryEntity, Integer pageNo, Integer pageSize) {
        PageReq pageReq = new PageReq();
        pageReq.setCurrent((long) pageNo);
        pageReq.setSize((long) pageSize);

        // 按照创建时间倒序排列
        OrderParam createTimeParam = new OrderParam();
        createTimeParam.setField("create_time");
        createTimeParam.setOrder("desc");
        pageReq.setOrderParam(Arrays.asList(createTimeParam));

        return procurementApplicationRepository.queryPageList(queryEntity, pageReq);
    }

    /**
     * 生成采购申请单号
     * 格式：PA + YYYYMMDD + 4位序号
     *
     * @return 采购申请单号
     */
    public String generateApplicationNo() {
        return procurementApplicationRepository.generateApplicationNo();
    }

    /**
     * 保存采购申请（包含明细）
     * 
     * @param entity 采购申请实体
     * @return 保存后的实体
     */
    @Transactional(rollbackFor = Exception.class)
    public ProcurementApplicationEntity save(ProcurementApplicationEntity entity) {
        // 参数校验
        validateProcurementApplication(entity);
        
        // 计算价格信息
        entity.calculatePrices();
        
        // 计算明细价格
        if (entity.getDetails() != null) {
            entity.getDetails().forEach(detail -> detail.calculatePrices());
        }
        
        // 保存到数据库
        ProcurementApplicationEntity savedEntity = procurementApplicationRepository.save(entity);

        // 更新采购执行依据的已采购量
        try {
            procurementApplicationRepository.updateExecutionBasisPurchasedQuantity(savedEntity);
            log.info("更新采购执行依据已采购量成功，采购申请ID: {}", savedEntity.getId());
        } catch (Exception e) {
            log.error("更新采购执行依据已采购量失败，采购申请ID: {}", savedEntity.getId(), e);
            // 这里可以选择抛出异常回滚事务，或者记录错误继续执行
            throw new RuntimeException("保存采购申请成功，但更新采购执行依据失败: " + e.getMessage(), e);
        }

        return savedEntity;
    }

    /**
     * 更新采购申请（包含明细）
     *
     * @param entity 采购申请实体
     * @return 更新后的实体
     */
    @Transactional(rollbackFor = Exception.class)
    public ProcurementApplicationEntity update(ProcurementApplicationEntity entity) {
        // 检查申请是否存在
        ProcurementApplicationEntity existingEntity = procurementApplicationRepository.getById(entity.getId());
        if (existingEntity == null) {
            throw new IllegalArgumentException("采购申请不存在");
        }

        // 参数校验
        validateProcurementApplication(entity);

        // 计算价格信息
        entity.calculatePrices();

        // 计算明细价格
        if (entity.getDetails() != null) {
            entity.getDetails().forEach(detail -> detail.calculatePrices());
        }

        // 更新到数据库
        ProcurementApplicationEntity updatedEntity = procurementApplicationRepository.update(entity);

        // 差量更新采购执行依据的已采购量和已支出金额
        try {
            procurementApplicationRepository.updateExecutionBasisPurchasedQuantityByDelta(existingEntity, updatedEntity);
            log.info("差量更新采购执行依据成功，采购申请ID: {}", updatedEntity.getId());
        } catch (Exception e) {
            log.error("差量更新采购执行依据失败，采购申请ID: {}", updatedEntity.getId(), e);
            // 抛出异常回滚事务
            throw new RuntimeException("更新采购申请成功，但差量更新采购执行依据失败: " + e.getMessage(), e);
        }

        return updatedEntity;
    }

    /**
     * 根据ID查询采购申请详情（包含明细）
     * 
     * @param id 采购申请ID
     * @return 采购申请实体
     */
    public ProcurementApplicationEntity getById(String id) {
        if (id == null || id.trim().isEmpty()) {
            throw new IllegalArgumentException("采购申请ID不能为空");
        }
        return procurementApplicationRepository.getById(id);
    }

    /**
     * 根据ID删除采购申请
     * 
     * @param id 采购申请ID
     */
    @Transactional(rollbackFor = Exception.class)
    public void deleteById(String id) {
        if (id == null || id.trim().isEmpty()) {
            throw new IllegalArgumentException("采购申请ID不能为空");
        }
        procurementApplicationRepository.deleteById(id);
    }

    /**
     * 批量删除采购申请
     * 
     * @param ids 采购申请ID列表
     */
    @Transactional(rollbackFor = Exception.class)
    public void deleteBatch(List<String> ids) {
        if (ids == null || ids.isEmpty()) {
            throw new IllegalArgumentException("采购申请ID列表不能为空");
        }
        procurementApplicationRepository.deleteBatch(ids);
    }

    /**
     * 根据申请单号查询采购申请
     *
     * @param applicationNo 申请单号
     * @return 采购申请实体
     */
    public ProcurementApplicationEntity findByApplicationNo(String applicationNo) {
        if (applicationNo == null || applicationNo.trim().isEmpty()) {
            throw new IllegalArgumentException("申请单号不能为空");
        }
        return procurementApplicationRepository.findByApplicationNo(applicationNo);
    }

    /**
     * 获取主表导出数据列表（不包含明细数据）
     *
     * @param queryEntity 查询条件
     * @return 主表导出数据列表
     */
    public List<ProcurementApplicationEntity> getMainTableExportList(ProcurementApplicationEntity queryEntity) {
        try {
            log.info("开始获取采购申请主表导出数据，查询条件：{}", queryEntity);

            // 使用仓储的findAllMainTable方法获取所有符合条件的主表数据（不包含明细）
            List<ProcurementApplicationEntity> entityList = procurementApplicationRepository.findAllMainTable(queryEntity);

            // 处理空结果的情况
            if (entityList == null) {
                log.info("查询主表结果为空，返回空列表");
                return Collections.emptyList();
            }

            log.info("获取主表导出数据成功，共{}条记录", entityList.size());
            return entityList;

        } catch (Exception e) {
            log.error("获取主表导出数据失败", e);
            // 返回空列表而不是抛出异常，这样可以导出空Excel
            log.warn("获取主表导出数据失败，返回空列表以支持导出空Excel");
            return Collections.emptyList();
        }
    }

    /**
     * 加权均分本次采购量到明细行
     * 
     * @param entity 采购申请实体
     * @return 分配后的实体
     */
    public ProcurementApplicationEntity distributeQuantityToDetails(ProcurementApplicationEntity entity) {
        if (entity == null) {
            throw new IllegalArgumentException("采购申请实体不能为空");
        }
        
        entity.distributeQuantityToDetails();
        return entity;
    }

    /**
     * 校验采购申请数据
     * 
     * @param entity 采购申请实体
     */
    private void validateProcurementApplication(ProcurementApplicationEntity entity) {
        if (entity == null) {
            throw new IllegalArgumentException("采购申请实体不能为空");
        }

        // 校验基本信息
        if (entity.getMaterialCode() == null || entity.getMaterialCode().trim().isEmpty()) {
            throw new IllegalArgumentException("物料编码不能为空");
        }
        if (entity.getMaterialName() == null || entity.getMaterialName().trim().isEmpty()) {
            throw new IllegalArgumentException("物料名称不能为空");
        }
        if (entity.getCurrentPurchaseQuantity() == null || entity.getCurrentPurchaseQuantity().compareTo(java.math.BigDecimal.ZERO) <= 0) {
            throw new IllegalArgumentException("本次采购量必须大于0");
        }

        // 校验本次采购量不能超过可采购量
        if (!entity.validatePurchaseQuantity()) {
            throw new IllegalArgumentException("本次采购量不能超过可采购量");
        }

        // 校验明细数据
        if (entity.getDetails() == null || entity.getDetails().isEmpty()) {
            throw new IllegalArgumentException("明细列表不能为空");
        }

        // 校验明细行采购量之和是否等于主表采购量
        if (!entity.validateDetailQuantitySum()) {
            throw new IllegalArgumentException("明细行采购量之和必须等于主表采购量");
        }

        // 校验每个明细行的采购量不能超过可采购量
        for (int i = 0; i < entity.getDetails().size(); i++) {
            var detail = entity.getDetails().get(i);
            if (!detail.validatePurchaseQuantity()) {
                throw new IllegalArgumentException("第" + (i + 1) + "行明细的本次采购量不能超过可采购量");
            }
        }
    }
}
