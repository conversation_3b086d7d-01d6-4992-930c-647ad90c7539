package com.cdkit.modules.cm.application.procurement.converter;

import com.cdkit.modules.cm.api.procurement.dto.ProcurementApplicationDTO;
import com.cdkit.modules.cm.api.procurement.dto.ProcurementApplicationDetailDTO;
import com.cdkit.modules.cm.api.procurement.dto.ProcurementApplicationQueryDTO;
import com.cdkit.modules.cm.domain.procurement.entity.ProcurementApplicationEntity;
import com.cdkit.modules.cm.domain.procurement.entity.ProcurementApplicationDetailEntity;

import java.util.List;
import java.util.stream.Collectors;

/**
 * 采购申请DTO转换器
 * 
 * <AUTHOR>
 * @date 2025-08-26
 */
public class ProcurementApplicationDTOConverter {

    /**
     * DTO转领域实体
     */
    public static ProcurementApplicationEntity toEntity(ProcurementApplicationDTO dto) {
        if (dto == null) {
            return null;
        }
        
        ProcurementApplicationEntity entity = ProcurementApplicationEntity.builder()
                .id(dto.getId())
                .applicationNo(dto.getApplicationNo())
                .materialCode(dto.getMaterialCode())
                .materialName(dto.getMaterialName())
                .unit(dto.getUnit())
                .quarter(dto.getQuarter())
                .startDate(dto.getStartDate())
                .endDate(dto.getEndDate())
                .budgetTotalQuantity(dto.getBudgetTotalQuantity())
                .purchasedQuantity(dto.getPurchasedQuantity())
                .availableQuantity(dto.getAvailableQuantity())
                .currentPurchaseQuantity(dto.getCurrentPurchaseQuantity())
                .unitPriceExcludingTax(dto.getUnitPriceExcludingTax())
                .totalPriceExcludingTax(dto.getTotalPriceExcludingTax())
                .totalPriceIncludingTax(dto.getTotalPriceIncludingTax())
                .taxAmount(dto.getTaxAmount())
                .executionBasisId(dto.getExecutionBasisId())
                .createTime(dto.getCreateTime())
                .createBy(dto.getCreateBy())
                .updateTime(dto.getUpdateTime())
                .updateBy(dto.getUpdateBy())
                .tenantId(dto.getTenantId())
                .delFlag(dto.getDelFlag())
                .sysOrgCode(dto.getSysOrgCode())
                .build();
        
        // 转换明细
        if (dto.getDetails() != null) {
            List<ProcurementApplicationDetailEntity> detailEntities = dto.getDetails().stream()
                    .map(ProcurementApplicationDTOConverter::toDetailEntity)
                    .collect(Collectors.toList());
            entity.setDetails(detailEntities);
        }
        
        return entity;
    }

    /**
     * 领域实体转DTO
     */
    public static ProcurementApplicationDTO toDTO(ProcurementApplicationEntity entity) {
        if (entity == null) {
            return null;
        }
        
        ProcurementApplicationDTO dto = ProcurementApplicationDTO.builder()
                .id(entity.getId())
                .applicationNo(entity.getApplicationNo())
                .materialCode(entity.getMaterialCode())
                .materialName(entity.getMaterialName())
                .unit(entity.getUnit())
                .quarter(entity.getQuarter())
                .startDate(entity.getStartDate())
                .endDate(entity.getEndDate())
                .budgetTotalQuantity(entity.getBudgetTotalQuantity())
                .purchasedQuantity(entity.getPurchasedQuantity())
                .availableQuantity(entity.getAvailableQuantity())
                .currentPurchaseQuantity(entity.getCurrentPurchaseQuantity())
                .unitPriceExcludingTax(entity.getUnitPriceExcludingTax())
                .totalPriceExcludingTax(entity.getTotalPriceExcludingTax())
                .totalPriceIncludingTax(entity.getTotalPriceIncludingTax())
                .taxAmount(entity.getTaxAmount())
                .executionBasisId(entity.getExecutionBasisId())
                .createTime(entity.getCreateTime())
                .createBy(entity.getCreateBy())
                .updateTime(entity.getUpdateTime())
                .updateBy(entity.getUpdateBy())
                .tenantId(entity.getTenantId())
                .delFlag(entity.getDelFlag())
                .sysOrgCode(entity.getSysOrgCode())
                .build();
        
        // 转换明细
        if (entity.getDetails() != null) {
            List<ProcurementApplicationDetailDTO> detailDTOs = entity.getDetails().stream()
                    .map(ProcurementApplicationDTOConverter::toDetailDTO)
                    .collect(Collectors.toList());
            dto.setDetails(detailDTOs);
        }
        
        return dto;
    }

    /**
     * 明细DTO转领域实体
     */
    public static ProcurementApplicationDetailEntity toDetailEntity(ProcurementApplicationDetailDTO dto) {
        if (dto == null) {
            return null;
        }
        
        return ProcurementApplicationDetailEntity.builder()
                .id(dto.getId())
                .applicationId(dto.getApplicationId())
                .budgetCode(dto.getBudgetCode())
                .budgetName(dto.getBudgetName())
                .budgetTotalQuantity(dto.getBudgetTotalQuantity())
                .availableQuantity(dto.getAvailableQuantity())
                .currentPurchaseQuantity(dto.getCurrentPurchaseQuantity())
                .unitPriceExcludingTax(dto.getUnitPriceExcludingTax())
                .totalPriceExcludingTax(dto.getTotalPriceExcludingTax())
                .totalPriceIncludingTax(dto.getTotalPriceIncludingTax())
                .taxAmount(dto.getTaxAmount())
                .executionBasisDetailId(dto.getExecutionBasisDetailId())
                .quarterlyBudgetId(dto.getQuarterlyBudgetId())
                .createTime(dto.getCreateTime())
                .createBy(dto.getCreateBy())
                .updateTime(dto.getUpdateTime())
                .updateBy(dto.getUpdateBy())
                .tenantId(dto.getTenantId())
                .delFlag(dto.getDelFlag())
                .sysOrgCode(dto.getSysOrgCode())
                .build();
    }

    /**
     * 明细领域实体转DTO
     */
    public static ProcurementApplicationDetailDTO toDetailDTO(ProcurementApplicationDetailEntity entity) {
        if (entity == null) {
            return null;
        }
        
        return ProcurementApplicationDetailDTO.builder()
                .id(entity.getId())
                .applicationId(entity.getApplicationId())
                .budgetCode(entity.getBudgetCode())
                .budgetName(entity.getBudgetName())
                .budgetTotalQuantity(entity.getBudgetTotalQuantity())
                .availableQuantity(entity.getAvailableQuantity())
                .currentPurchaseQuantity(entity.getCurrentPurchaseQuantity())
                .unitPriceExcludingTax(entity.getUnitPriceExcludingTax())
                .totalPriceExcludingTax(entity.getTotalPriceExcludingTax())
                .totalPriceIncludingTax(entity.getTotalPriceIncludingTax())
                .taxAmount(entity.getTaxAmount())
                .executionBasisDetailId(entity.getExecutionBasisDetailId())
                .quarterlyBudgetId(entity.getQuarterlyBudgetId())
                .createTime(entity.getCreateTime())
                .createBy(entity.getCreateBy())
                .updateTime(entity.getUpdateTime())
                .updateBy(entity.getUpdateBy())
                .tenantId(entity.getTenantId())
                .delFlag(entity.getDelFlag())
                .sysOrgCode(entity.getSysOrgCode())
                .build();
    }

    /**
     * 批量转换DTO列表
     */
    public static List<ProcurementApplicationDTO> toDTOList(List<ProcurementApplicationEntity> entities) {
        if (entities == null) {
            return null;
        }
        return entities.stream()
                .map(ProcurementApplicationDTOConverter::toDTO)
                .collect(Collectors.toList());
    }

    /**
     * 批量转换实体列表
     */
    public static List<ProcurementApplicationEntity> toEntityList(List<ProcurementApplicationDTO> dtos) {
        if (dtos == null) {
            return null;
        }
        return dtos.stream()
                .map(ProcurementApplicationDTOConverter::toEntity)
                .collect(Collectors.toList());
    }

    /**
     * 查询DTO转领域实体
     */
    public static ProcurementApplicationEntity toEntity(ProcurementApplicationQueryDTO queryDTO) {
        if (queryDTO == null) {
            return null;
        }

        return ProcurementApplicationEntity.builder()
                .applicationNo(queryDTO.getApplicationNo())
                .materialCode(queryDTO.getMaterialCode())
                .materialName(queryDTO.getMaterialName())
                .unit(queryDTO.getUnit())
                .quarter(queryDTO.getQuarter())
                .createBy(queryDTO.getCreateBy())
                .createTime(queryDTO.getCreateTime())
                .build();
    }
}
