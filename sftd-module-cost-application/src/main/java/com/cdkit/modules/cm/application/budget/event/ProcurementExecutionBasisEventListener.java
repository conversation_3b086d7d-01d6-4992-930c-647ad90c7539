package com.cdkit.modules.cm.application.budget.event;

import com.cdkit.modules.cm.domain.budget.event.QuarterlyBudgetLockedEvent;
import com.cdkit.modules.cm.domain.procurement.service.ProcurementExecutionBasisGenerationService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.event.EventListener;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;
import org.springframework.transaction.event.TransactionPhase;
import org.springframework.transaction.event.TransactionalEventListener;

/**
 * 采购执行依据事件监听器
 * 监听季度预算锁定事件，自动生成采购执行依据
 * 
 * <AUTHOR>
 * @date 2025-08-21
 */
@Component
@RequiredArgsConstructor
@Slf4j
public class ProcurementExecutionBasisEventListener {

    private final ProcurementExecutionBasisGenerationService procurementExecutionBasisGenerationService;

    /**
     * 处理季度预算锁定事件
     * 使用@TransactionalEventListener确保在预算锁定事务提交后才执行
     * 使用@Async异步处理，避免阻塞主业务流程
     *
     * @param event 季度预算锁定事件
     */
    @Async("eventTaskExecutor")
    @TransactionalEventListener(phase = TransactionPhase.AFTER_COMMIT)
    public void handleQuarterlyBudgetLocked(QuarterlyBudgetLockedEvent event) {
        log.info("接收到季度预算锁定事件: {}", event);

        try {
            // 调用领域服务生成采购执行依据
            ProcurementExecutionBasisGenerationService.GenerationResult result = 
                procurementExecutionBasisGenerationService.generateFromQuarterlyBudget(
                    event.getBudgetId(), 
                    event.getTenantId(), 
                    event.getOperatorBy()
                );

            if (result.isSuccess()) {
                log.info("季度预算锁定后采购执行依据生成成功: {}, 事件: {}", result, event.getEventDescription());
            } else {
                log.error("季度预算锁定后采购执行依据生成失败: {}, 事件: {}", result, event.getEventDescription());
                
                // 这里可以考虑发送告警通知或记录到错误日志表
                handleGenerationFailure(event, result);
            }

        } catch (Exception e) {
            log.error("处理季度预算锁定事件时发生异常, 事件: {}", event, e);
            
            // 异常处理：记录错误信息，但不影响主流程
            handleGenerationException(event, e);
        }
    }

    /**
     * 处理生成失败的情况
     * 可以扩展为发送告警、记录错误日志等
     * 
     * @param event 事件信息
     * @param result 生成结果
     */
    private void handleGenerationFailure(QuarterlyBudgetLockedEvent event, 
                                       ProcurementExecutionBasisGenerationService.GenerationResult result) {
        log.warn("采购执行依据生成失败，需要人工处理 - 预算ID: {}, 预算单号: {}, 错误信息: {}", 
                event.getBudgetId(), event.getQuarterlyBudgetNo(), result.getErrorMessage());
        
        // TODO: 可以在这里实现以下功能：
        // 1. 发送邮件或短信告警给相关人员
        // 2. 记录到错误处理表，供后续人工处理
        // 3. 发送系统通知
        // 4. 写入审计日志
    }

    /**
     * 处理生成过程中的异常
     * 
     * @param event 事件信息
     * @param exception 异常信息
     */
    private void handleGenerationException(QuarterlyBudgetLockedEvent event, Exception exception) {
        log.error("采购执行依据生成过程中发生异常，需要人工处理 - 预算ID: {}, 预算单号: {}, 异常信息: {}", 
                event.getBudgetId(), event.getQuarterlyBudgetNo(), exception.getMessage());
        
        // TODO: 可以在这里实现以下功能：
        // 1. 发送紧急告警
        // 2. 记录到异常处理表
        // 3. 触发重试机制
        // 4. 写入系统错误日志
    }

    /**
     * 同步事件监听器（备用）
     * 在某些特殊情况下，如果需要同步处理，可以使用此方法
     * 默认情况下不启用，通过配置控制
     */
    @EventListener
    // @ConditionalOnProperty(name = "cost.procurement.execution-basis.sync-generation", havingValue = "true")
    public void handleQuarterlyBudgetLockedSync(QuarterlyBudgetLockedEvent event) {
        // 同步处理逻辑（默认不启用）
        log.debug("同步事件监听器收到季度预算锁定事件（未启用）: {}", event);
    }
}
