package com.cdkit.modules.cm.application.budget.event;

import com.cdkit.modules.cm.domain.budget.event.QuarterlyBudgetLockedEvent;
import com.cdkit.modules.cm.domain.procurement.service.ProcurementExecutionBasisGenerationService;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

/**
 * 采购执行依据事件监听器测试
 * 
 * <AUTHOR>
 * @date 2025-08-21
 */
@ExtendWith(MockitoExtension.class)
class ProcurementExecutionBasisEventListenerTest {

    @Mock
    private ProcurementExecutionBasisGenerationService procurementExecutionBasisGenerationService;

    @InjectMocks
    private ProcurementExecutionBasisEventListener eventListener;

    private QuarterlyBudgetLockedEvent testEvent;

    @BeforeEach
    void setUp() {
        testEvent = new QuarterlyBudgetLockedEvent(
                "test-budget-id",
                1,
                "JDYS20250821001",
                "测试季度预算",
                "test-user"
        );
    }

    @Test
    void testHandleQuarterlyBudgetLocked_Success() {
        // Given
        ProcurementExecutionBasisGenerationService.GenerationResult successResult = 
            ProcurementExecutionBasisGenerationService.GenerationResult.success(2, 5, 3, 1000);
        when(procurementExecutionBasisGenerationService.generateFromQuarterlyBudget(
                anyString(), anyInt(), anyString())).thenReturn(successResult);

        // When
        eventListener.handleQuarterlyBudgetLocked(testEvent);

        // Then
        verify(procurementExecutionBasisGenerationService).generateFromQuarterlyBudget(
                "test-budget-id", 1, "test-user");
    }

    @Test
    void testHandleQuarterlyBudgetLocked_GenerationFailure() {
        // Given
        ProcurementExecutionBasisGenerationService.GenerationResult failureResult = 
            ProcurementExecutionBasisGenerationService.GenerationResult.failure("生成失败", 500);
        when(procurementExecutionBasisGenerationService.generateFromQuarterlyBudget(
                anyString(), anyInt(), anyString())).thenReturn(failureResult);

        // When
        eventListener.handleQuarterlyBudgetLocked(testEvent);

        // Then
        verify(procurementExecutionBasisGenerationService).generateFromQuarterlyBudget(
                "test-budget-id", 1, "test-user");
        // 验证失败处理逻辑被调用（通过日志验证，这里只验证服务调用）
    }

    @Test
    void testHandleQuarterlyBudgetLocked_Exception() {
        // Given
        when(procurementExecutionBasisGenerationService.generateFromQuarterlyBudget(
                anyString(), anyInt(), anyString())).thenThrow(new RuntimeException("服务异常"));

        // When
        eventListener.handleQuarterlyBudgetLocked(testEvent);

        // Then
        verify(procurementExecutionBasisGenerationService).generateFromQuarterlyBudget(
                "test-budget-id", 1, "test-user");
        // 验证异常处理逻辑被调用（通过日志验证，这里只验证服务调用）
    }

    @Test
    void testHandleQuarterlyBudgetLockedSync() {
        // When
        eventListener.handleQuarterlyBudgetLockedSync(testEvent);

        // Then
        // 同步监听器默认不启用，只验证方法可以正常调用
        verifyNoInteractions(procurementExecutionBasisGenerationService);
    }

    @Test
    void testEventWithMinimalData() {
        // Given
        QuarterlyBudgetLockedEvent minimalEvent = new QuarterlyBudgetLockedEvent("budget-id", 1);
        ProcurementExecutionBasisGenerationService.GenerationResult successResult = 
            ProcurementExecutionBasisGenerationService.GenerationResult.success(1, 2, 1, 500);
        when(procurementExecutionBasisGenerationService.generateFromQuarterlyBudget(
                anyString(), anyInt(), isNull())).thenReturn(successResult);

        // When
        eventListener.handleQuarterlyBudgetLocked(minimalEvent);

        // Then
        verify(procurementExecutionBasisGenerationService).generateFromQuarterlyBudget(
                "budget-id", 1, null);
    }
}
