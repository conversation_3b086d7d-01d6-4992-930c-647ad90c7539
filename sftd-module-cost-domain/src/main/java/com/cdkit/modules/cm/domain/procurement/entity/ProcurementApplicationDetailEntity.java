package com.cdkit.modules.cm.domain.procurement.entity;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 采购申请明细领域实体
 * 
 * <AUTHOR>
 * @date 2025-08-26
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ProcurementApplicationDetailEntity implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * UUID主键
     */
    private String id;

    /**
     * 关联采购申请主表ID
     */
    private String applicationId;

    /**
     * 预算编码
     */
    private String budgetCode;

    /**
     * 预算名称
     */
    private String budgetName;

    /**
     * 预算总量
     */
    private BigDecimal budgetTotalQuantity;

    /**
     * 可采购量
     */
    private BigDecimal availableQuantity;

    /**
     * 本次采购量
     */
    private BigDecimal currentPurchaseQuantity;

    /**
     * 不含税单价（元）
     */
    private BigDecimal unitPriceExcludingTax;

    /**
     * 不含税总价（元）
     */
    private BigDecimal totalPriceExcludingTax;

    /**
     * 含税总价（元）
     */
    private BigDecimal totalPriceIncludingTax;

    /**
     * 税额（元）
     */
    private BigDecimal taxAmount;

    /**
     * 关联采购执行依据明细表ID
     */
    private String executionBasisDetailId;

    /**
     * 关联季度预算主表ID
     */
    private String quarterlyBudgetId;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 创建人
     */
    private String createBy;

    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     * 更新人
     */
    private String updateBy;

    /**
     * 租户ID
     */
    private Integer tenantId;

    /**
     * 删除标识
     */
    private Integer delFlag;

    /**
     * 所属部门代码
     */
    private String sysOrgCode;

    /**
     * 计算价格信息
     * 根据本次采购量和不含税单价计算各项价格
     */
    public void calculatePrices() {
        if (this.currentPurchaseQuantity != null && this.unitPriceExcludingTax != null) {
            // 计算不含税总价
            this.totalPriceExcludingTax = this.currentPurchaseQuantity.multiply(this.unitPriceExcludingTax);
            
            // 计算含税总价（假设税率13%）
            this.totalPriceIncludingTax = this.totalPriceExcludingTax.multiply(new BigDecimal("1.13"));
            
            // 计算税额
            this.taxAmount = this.totalPriceIncludingTax.subtract(this.totalPriceExcludingTax);
        }
    }

    /**
     * 校验本次采购量是否超过可采购量
     */
    public boolean validatePurchaseQuantity() {
        if (this.currentPurchaseQuantity == null || this.availableQuantity == null) {
            return false;
        }
        return this.currentPurchaseQuantity.compareTo(this.availableQuantity) <= 0;
    }
}
