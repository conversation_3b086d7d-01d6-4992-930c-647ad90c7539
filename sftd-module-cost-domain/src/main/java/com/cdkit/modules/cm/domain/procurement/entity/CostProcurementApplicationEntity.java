package com.cdkit.modules.cm.domain.procurement.entity;

import java.util.List;
import lombok.Data;
import com.cdkitframework.poi.excel.annotation.Excel;
import com.cdkitframework.poi.excel.annotation.ExcelCollection;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.springframework.format.annotation.DateTimeFormat;
import java.util.Date;

import io.swagger.v3.oas.annotations.media.Schema;


/**
 * @Description: 采购申请
 * @Author: cdkit-boot
 * @Date:   2025-08-26
 * @Version: V1.0
 */
@Data
@Schema(description="cost_procurement_applicationPage对象")
public class CostProcurementApplicationEntity {

	/**UUID主键*/
	@Schema(description = "UUID主键")
    private String id;
	/**采购申请单号（自动生成）*/
	@Excel(name = "采购申请单号（自动生成）", width = 15)
	@Schema(description = "采购申请单号（自动生成）")
    private String applicationNo;
	/**物料编码*/
	@Excel(name = "物料编码", width = 15)
	@Schema(description = "物料编码")
    private String materialCode;
	/**物料名称*/
	@Excel(name = "物料名称", width = 15)
	@Schema(description = "物料名称")
    private String materialName;
	/**计量单位*/
	@Excel(name = "计量单位", width = 15)
	@Schema(description = "计量单位")
    private String unit;
	/**所在季度(如：2025年第一季度)*/
	@Excel(name = "所在季度(如：2025年第一季度)", width = 15)
	@Schema(description = "所在季度(如：2025年第一季度)")
    private String quarter;
	/**开始时间（年月，如：2025-01）*/
	@Excel(name = "开始时间（年月，如：2025-01）", width = 15)
	@Schema(description = "开始时间（年月，如：2025-01）")
    private String startDate;
	/**结束时间（年月，如：2025-03）*/
	@Excel(name = "结束时间（年月，如：2025-03）", width = 15)
	@Schema(description = "结束时间（年月，如：2025-03）")
    private String endDate;
	/**预算总量*/
	@Excel(name = "预算总量", width = 15)
	@Schema(description = "预算总量")
    private java.math.BigDecimal budgetTotalQuantity;
	/**已采购量*/
	@Excel(name = "已采购量", width = 15)
	@Schema(description = "已采购量")
    private java.math.BigDecimal purchasedQuantity;
	/**可采购量*/
	@Excel(name = "可采购量", width = 15)
	@Schema(description = "可采购量")
    private java.math.BigDecimal availableQuantity;
	/**本次采购量*/
	@Excel(name = "本次采购量", width = 15)
	@Schema(description = "本次采购量")
    private java.math.BigDecimal currentPurchaseQuantity;
	/**不含税单价（元）*/
	@Excel(name = "不含税单价（元）", width = 15)
	@Schema(description = "不含税单价（元）")
    private java.math.BigDecimal unitPriceExcludingTax;
	/**不含税总价（元）*/
	@Excel(name = "不含税总价（元）", width = 15)
	@Schema(description = "不含税总价（元）")
    private java.math.BigDecimal totalPriceExcludingTax;
	/**含税总价（元）*/
	@Excel(name = "含税总价（元）", width = 15)
	@Schema(description = "含税总价（元）")
    private java.math.BigDecimal totalPriceIncludingTax;
	/**税额（元）*/
	@Excel(name = "税额（元）", width = 15)
	@Schema(description = "税额（元）")
    private java.math.BigDecimal taxAmount;
	/**关联采购执行依据主表ID*/
	@Excel(name = "关联采购执行依据主表ID", width = 15)
	@Schema(description = "关联采购执行依据主表ID")
    private String executionBasisId;
	/**附件URL*/
	@Schema(description = "附件URL")
	private String attachmentUrl;
	/**创建时间*/
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
	@Schema(description = "创建时间")
    private Date createTime;
	/**创建人*/
	@Schema(description = "创建人")
    private String createBy;
	/**更新时间*/
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
	@Schema(description = "更新时间")
    private Date updateTime;
	/**更新人*/
	@Schema(description = "更新人")
    private String updateBy;
	/**租户ID*/
	@Excel(name = "租户ID", width = 15)
	@Schema(description = "租户ID")
    private Integer tenantId;
	/**删除标识 0:未删除 1:删除*/
	@Excel(name = "删除标识 0:未删除 1:删除", width = 15)
	@Schema(description = "删除标识 0:未删除 1:删除")
    private Integer delFlag;
	/**所属部门代码*/
	@Schema(description = "所属部门代码")
    private String sysOrgCode;

	@ExcelCollection(name="采购申请明细信息")
	@Schema(description = "采购申请明细信息")
	private List<CostProcurementApplicationDetailEntity> costProcurementApplicationDetailList;

}
