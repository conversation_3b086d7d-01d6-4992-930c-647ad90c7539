package com.cdkit.modules.cm.domain.procurement.repository;

import com.cdkit.modules.cm.domain.procurement.mode.entity.ProcurementExecutionBasisEntity;
import com.cdkit.modules.cm.domain.procurement.mode.entity.ProcurementExecutionBasisDetailEntity;

import java.time.LocalDate;
import java.util.List;

/**
 * 采购执行依据仓储接口
 * 
 * <AUTHOR>
 * @date 2025-08-21
 */
public interface ProcurementExecutionBasisRepository {

    /**
     * 保存采购执行依据主表
     * 
     * @param entity 主表实体
     * @return 保存后的实体
     */
    ProcurementExecutionBasisEntity save(ProcurementExecutionBasisEntity entity);

    /**
     * 批量保存采购执行依据主表
     * 
     * @param entities 主表实体列表
     * @return 保存后的实体列表
     */
    List<ProcurementExecutionBasisEntity> saveBatch(List<ProcurementExecutionBasisEntity> entities);

    /**
     * 保存采购执行依据明细
     * 
     * @param entity 明细实体
     * @return 保存后的实体
     */
    ProcurementExecutionBasisDetailEntity saveDetail(ProcurementExecutionBasisDetailEntity entity);

    /**
     * 批量保存采购执行依据明细
     * 
     * @param entities 明细实体列表
     * @return 保存后的实体列表
     */
    List<ProcurementExecutionBasisDetailEntity> saveDetailBatch(List<ProcurementExecutionBasisDetailEntity> entities);

    /**
     * 根据物料编码和时间周期查询现有的执行依据
     * 
     * @param materialCode 物料编码
     * @param startDate 开始时间
     * @param endDate 结束时间
     * @param tenantId 租户ID
     * @return 执行依据实体
     */
    ProcurementExecutionBasisEntity findByMaterialAndPeriod(String materialCode, LocalDate startDate, 
                                                           LocalDate endDate, Integer tenantId);

    /**
     * 根据季度预算ID删除相关的执行依据数据
     * 
     * @param quarterlyBudgetId 季度预算ID
     * @param tenantId 租户ID
     */
    void deleteByQuarterlyBudgetId(String quarterlyBudgetId, Integer tenantId);

    /**
     * 根据执行依据ID查询明细列表
     * 
     * @param executionBasisId 执行依据ID
     * @return 明细列表
     */
    List<ProcurementExecutionBasisDetailEntity> findDetailsByExecutionBasisId(String executionBasisId);

    /**
     * 统计生成结果
     * 
     * @param quarterlyBudgetId 季度预算ID
     * @param tenantId 租户ID
     * @return 统计结果 [主表数量, 明细数量, 物料数量]
     */
    int[] countGenerationResult(String quarterlyBudgetId, Integer tenantId);

    /**
     * 根据租户ID查询所有执行依据
     * 
     * @param tenantId 租户ID
     * @return 执行依据列表
     */
    List<ProcurementExecutionBasisEntity> findByTenantId(Integer tenantId);

    /**
     * 更新已采购量统计
     * 
     * @param materialCode 物料编码
     * @param startDate 开始时间
     * @param endDate 结束时间
     * @param tenantId 租户ID
     */
    void updatePurchasedAmountStatistics(String materialCode, LocalDate startDate, 
                                       LocalDate endDate, Integer tenantId);
}
