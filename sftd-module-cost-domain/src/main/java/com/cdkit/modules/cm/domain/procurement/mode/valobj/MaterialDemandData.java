package com.cdkit.modules.cm.domain.procurement.mode.valobj;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.time.LocalDate;

/**
 * 物料需求数据值对象
 * 用于从季度预算明细中提取的物料需求信息
 * 
 * <AUTHOR>
 * @date 2025-08-21
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class MaterialDemandData {

    /**
     * 季度预算ID
     */
    private String quarterlyBudgetId;

    /**
     * 季度预算编号
     */
    private String quarterlyBudgetNo;

    /**
     * 季度预算名称
     */
    private String quarterlyBudgetName;

    /**
     * 季度
     */
    private String quarter;

    /**
     * 开始时间
     */
    private LocalDate startDate;

    /**
     * 结束时间
     */
    private LocalDate endDate;

    /**
     * 物料编码
     */
    private String materialCode;

    /**
     * 物料名称
     */
    private String materialName;

    /**
     * 计量单位
     */
    private String unit;

    /**
     * 需求量
     */
    private BigDecimal usageAmount;

    /**
     * 不含税单价
     */
    private BigDecimal unitPriceExcludingTax;

    /**
     * 不含税总价
     */
    private BigDecimal totalPriceExcludingTax;

    /**
     * 编制依据
     */
    private String compilationBasis;

    /**
     * 备注
     */
    private String remark;

    /**
     * 租户ID
     */
    private Integer tenantId;

    /**
     * 创建物料分组键
     * 用于按物料编码和时间周期分组
     */
    public String createGroupKey() {
        return String.format("%s_%s_%s", materialCode, startDate, endDate);
    }

    /**
     * 计算含税总价（假设税率13%）
     */
    public BigDecimal calculateIncludingTaxPrice() {
        if (totalPriceExcludingTax != null) {
            return totalPriceExcludingTax.multiply(new BigDecimal("1.13"));
        }
        return BigDecimal.ZERO;
    }

    /**
     * 验证数据完整性
     */
    public boolean isValid() {
        return quarterlyBudgetId != null && 
               materialCode != null && 
               materialName != null && 
               unit != null &&
               usageAmount != null && 
               unitPriceExcludingTax != null &&
               totalPriceExcludingTax != null &&
               startDate != null && 
               endDate != null &&
               tenantId != null;
    }
}
