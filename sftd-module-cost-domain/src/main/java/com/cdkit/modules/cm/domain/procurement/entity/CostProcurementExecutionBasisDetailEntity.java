package com.cdkit.modules.cm.domain.procurement.entity;

import java.io.Serializable;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableLogic;
import lombok.Data;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.springframework.format.annotation.DateTimeFormat;
import com.cdkitframework.poi.excel.annotation.Excel;
import java.util.Date;
import io.swagger.v3.oas.annotations.media.Schema;

/**
 * @Description: 采购执行依据明细表
 * @Author: cdkit-boot
 * @Date:   2025-08-21
 * @Version: V1.0
 */
@Schema(description="cost_procurement_execution_basis_detail对象")
@Data
@TableName("cost_procurement_execution_basis_detail")
public class CostProcurementExecutionBasisDetailEntity implements Serializable {
    private static final long serialVersionUID = 1L;

	/**UUID主键*/
	@TableId(type = IdType.ASSIGN_ID)
    @Schema(description = "UUID主键")
    private String id;
	/**关联采购执行依据主表ID*/
    @Schema(description = "关联采购执行依据主表ID")
    private String executionBasisId;
	/**预算编号*/
	@Excel(name = "预算编号", width = 15)
    @Schema(description = "预算编号")
    private String quarterlyBudgetNo;
	/**预算名称*/
	@Excel(name = "预算名称", width = 15)
    @Schema(description = "预算名称")
    private String quarterlyBudgetName;
	/**总需求量*/
	@Excel(name = "总需求量", width = 15)
    @Schema(description = "总需求量")
    private java.math.BigDecimal demandQuantity;
	/**计量单位*/
	@Excel(name = "计量单位", width = 15)
    @Schema(description = "计量单位")
    private String unit;
	/**不含税单价（元）*/
	@Excel(name = "不含税单价（元）", width = 15)
    @Schema(description = "不含税单价（元）")
    private java.math.BigDecimal unitPriceExcludingTax;
	/**不含税总价（元）*/
	@Excel(name = "不含税总价（元）", width = 15)
    @Schema(description = "不含税总价（元）")
    private java.math.BigDecimal totalPriceExcludingTax;
	/**含税总价（元）*/
	@Excel(name = "含税总价（元）", width = 15)
    @Schema(description = "含税总价（元）")
    private java.math.BigDecimal totalPriceIncludingTax;
	/**已采购量*/
	@Excel(name = "已采购量", width = 15)
    @Schema(description = "已采购量")
    private java.math.BigDecimal purchasedQuantity;
	/**可采购量*/
	@Excel(name = "可采购量", width = 15)
    @Schema(description = "可采购量")
    private java.math.BigDecimal availableQuantity;
	/**已支出不含税金额（元）*/
	@Excel(name = "已支出不含税金额（元）", width = 15)
    @Schema(description = "已支出不含税金额（元）")
    private java.math.BigDecimal spentAmountExcludingTax;
	/**已支出含税金额（元）*/
	@Excel(name = "已支出含税金额（元）", width = 15)
    @Schema(description = "已支出含税金额（元）")
    private java.math.BigDecimal spentAmountIncludingTax;
	/**剩余不含税金额（元）*/
	@Excel(name = "剩余不含税金额（元）", width = 15)
    @Schema(description = "剩余不含税金额（元）")
    private java.math.BigDecimal remainingAmountExcludingTax;
	/**剩余含税金额（元）*/
	@Excel(name = "剩余含税金额（元）", width = 15)
    @Schema(description = "剩余含税金额（元）")
    private java.math.BigDecimal remainingAmountIncludingTax;
	/**创建时间*/
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @Schema(description = "创建时间")
    private Date createTime;
	/**创建人*/
    @Schema(description = "创建人")
    private String createBy;
	/**更新时间*/
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @Schema(description = "更新时间")
    private Date updateTime;
	/**更新人*/
    @Schema(description = "更新人")
    private String updateBy;
	/**租户ID*/
	@Excel(name = "租户ID", width = 15)
    @Schema(description = "租户ID")
    private Integer tenantId;
	/**删除标识 0:未删除 1:删除*/
	@Excel(name = "删除标识 0:未删除 1:删除", width = 15)
    @Schema(description = "删除标识 0:未删除 1:删除")
    @TableLogic
    private Integer delFlag;
	/**所属部门代码*/
    @Schema(description = "所属部门代码")
    private String sysOrgCode;
}
