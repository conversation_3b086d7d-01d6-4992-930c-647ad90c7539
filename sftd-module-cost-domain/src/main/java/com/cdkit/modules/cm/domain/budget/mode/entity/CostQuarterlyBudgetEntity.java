package com.cdkit.modules.cm.domain.budget.mode.entity;

import java.util.List;
import lombok.Data;
import com.cdkitframework.poi.excel.annotation.Excel;
import com.cdkitframework.poi.excel.annotation.ExcelCollection;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.springframework.format.annotation.DateTimeFormat;
import java.util.Date;

import io.swagger.v3.oas.annotations.media.Schema;


/**
 * @Description: 季度预算主表
 * @Author: cdkit-boot
 * @Date:   2025-08-12
 * @Version: V1.0
 */
@Data
@Schema(description="cost_quarterly_budgetEntity对象")
public class CostQuarterlyBudgetEntity {

	/**UUID主键*/
	@Schema(description = "UUID主键")
    private String id;
	/**季度预算单号(JDYS+8位日期+3位流水)*/
	@Excel(name = "季度预算单号(JDYS+8位日期+3位流水)", width = 15)
	@Schema(description = "季度预算单号(JDYS+8位日期+3位流水)")
    private String quarterlyBudgetNo;
	/**季度预算编码(融合服务平台回传)*/
	@Excel(name = "季度预算编码(融合服务平台回传)", width = 15)
	@Schema(description = "季度预算编码(融合服务平台回传)")
    private String quarterlyBudgetCode;
	/**季度预算名称*/
	@Excel(name = "季度预算名称", width = 15)
	@Schema(description = "季度预算名称")
    private String quarterlyBudgetName;
	/**版本*/
	@Excel(name = "版本", width = 15)
	@Schema(description = "版本")
    private String version;
	/**状态(PENDING_LOCK-待锁定/APPROVING-审批中/LOCKED-已锁定/REJECTED-已驳回/CHANGED-已变更)*/
	@Excel(name = "状态(PENDING_LOCK-待锁定/APPROVING-审批中/LOCKED-已锁定/REJECTED-已驳回/CHANGED-已变更)", width = 15)
	@Schema(description = "状态(PENDING_LOCK-待锁定/APPROVING-审批中/LOCKED-已锁定/REJECTED-已驳回/CHANGED-已变更)")
    private String budgetStatus;
	/**季度计划ID*/
	private String quarterlyPlanId;
	/**季度计划编号*/
	@Excel(name = "季度计划编号", width = 15)
	@Schema(description = "季度计划编号")
    private String quarterlyPlanCode;
	/**季度计划名称*/
	@Excel(name = "季度计划名称", width = 15)
	@Schema(description = "季度计划名称")
    private String quarterlyPlanName;
	/**项目经理*/
	@Excel(name = "项目经理", width = 15)
	@Schema(description = "项目经理")
    private String projectManagerName;
	/**季度(如：2025年第一季度)*/
	@Excel(name = "季度(如：2025年第一季度)", width = 15)
	@Schema(description = "季度(如：2025年第一季度)")
    private String quarter;
	/**开始时间*/
	@Excel(name = "开始时间", width = 15, format = "yyyy-MM-dd")
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern="yyyy-MM-dd")
	@Schema(description = "开始时间")
    private Date startDate;
	/**结束时间*/
	@Excel(name = "结束时间", width = 15, format = "yyyy-MM-dd")
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern="yyyy-MM-dd")
	@Schema(description = "结束时间")
    private Date endDate;
	/**是否涉及多年预算(Y-是/N-否)*/
	@Excel(name = "是否涉及多年预算(Y-是/N-否)", width = 15)
	@Schema(description = "是否涉及多年预算(Y-是/N-否)")
    private String isMultiYearBudget;
	/**关联年度预算ID*/
	@Schema(description = "关联项目年度预算ID")
	private String annualBudgetId;
	/**年度预算编码*/
	@Excel(name = "年度预算编码", width = 15)
	@Schema(description = "年度预算编码")
    private String annualBudgetCode;
	/**年度预算名称*/
	@Excel(name = "年度预算名称", width = 15)
	@Schema(description = "年度预算名称")
    private String annualBudgetName;
	/**所属单位*/
	@Excel(name = "所属单位", width = 15)
	@Schema(description = "所属单位")
    private String professionalCompany;
	/**下属中心*/
	@Excel(name = "下属中心", width = 15)
	@Schema(description = "下属中心")
    private String center;
	/**项目名称*/
	@Excel(name = "项目名称", width = 15)
	@Schema(description = "项目名称")
    private String projectName;
	/**预算类型*/
	@Excel(name = "预算类型", width = 15)
	@Schema(description = "预算类型")
    private String budgetType;

	/**WBS编号*/
	@Excel(name = "WBS编号", width = 15)
	@Schema(description = "WBS编号")
	private String wbsCode;
	/**年度收入预算金额（不含税，元）*/
	@Excel(name = "年度收入预算金额（不含税，元）", width = 15)
	@Schema(description = "年度收入预算金额（不含税，元）")
    private java.math.BigDecimal annualRevenueBudget;
	/**年度支出预算金额（不含税，元）*/
	@Excel(name = "年度支出预算金额（不含税，元）", width = 15)
	@Schema(description = "年度支出预算金额（不含税，元）")
    private java.math.BigDecimal annualExpenditureBudget;
	/**市场项目名称*/
	@Excel(name = "市场项目名称", width = 15)
	@Schema(description = "市场项目名称")
    private String marketProjectName;
	/**客户名称*/
	@Excel(name = "客户名称", width = 15)
	@Schema(description = "客户名称")
    private String customerName;
	/**委托部门*/
	@Excel(name = "委托部门", width = 15)
	@Schema(description = "委托部门")
    private String entrustingDepartment;
	/**合同/任务名称*/
	@Excel(name = "合同/任务名称", width = 15)
	@Schema(description = "合同/任务名称")
    private String contractTaskName;
	/**合同/任务编号*/
	@Excel(name = "合同/任务编号", width = 15)
	@Schema(description = "合同/任务编号")
    private String contractTaskNo;
	/**合同签订/任务下发日期*/
	@Excel(name = "合同签订/任务下发日期", width = 15, format = "yyyy-MM-dd")
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern="yyyy-MM-dd")
	@Schema(description = "合同签订/任务下发日期")
    private Date contractSignDate;
	/**合同/任务截止日期*/
	@Excel(name = "合同/任务截止日期", width = 15, format = "yyyy-MM-dd")
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern="yyyy-MM-dd")
	@Schema(description = "合同/任务截止日期")
    private Date contractDeadline;
	/**预估收入金额（含税，元）*/
	@Excel(name = "预估收入金额（含税，元）", width = 15)
	@Schema(description = "预估收入金额（含税，元）")
    private java.math.BigDecimal estimatedRevenueWithTax;
	/**收入预算金额（不含税，元）*/
	@Excel(name = "收入预算金额（不含税，元）", width = 15)
	@Schema(description = "收入预算金额（不含税，元）")
    private java.math.BigDecimal revenueBudgetAmount;
	/**支出预算金额（不含税，元）*/
	@Excel(name = "支出预算金额（不含税，元）", width = 15)
	@Schema(description = "支出预算金额（不含税，元）")
    private java.math.BigDecimal expenditureBudgetAmount;
	/**净利润（元）*/
	@Excel(name = "净利润（元）", width = 15)
	@Schema(description = "净利润（元）")
    private java.math.BigDecimal netProfit;
	/**净利润率（%）*/
	@Excel(name = "净利润率（%）", width = 15)
	@Schema(description = "净利润率（%）")
    private java.math.BigDecimal netProfitRate;

	//-----------------------收入预算金额---------------------

	/**年度收入剩余预算金额（不含税，元）*/
	@Excel(name = "年度收入剩余预算金额（不含税，元）", width = 15)
	@Schema(description = "年度收入剩余预算金额（不含税，元）")
    private java.math.BigDecimal annualRevenueRemainingBudgetAmount;
	/**项目收入预算总额（元）*/
	@Excel(name = "项目收入预算总额（元）", width = 15)
	@Schema(description = "项目收入预算总额（元）")
    private java.math.BigDecimal projectRevenueBudgetTotalAmount;
	//------------------------------------------------------------
	/**年度支出剩余预算金额（不含税，元）*/
	@Excel(name = "年度支出剩余预算金额（不含税，元）", width = 15)
	@Schema(description = "年度支出剩余预算金额（不含税，元）")
    private java.math.BigDecimal annualExpenditureRemainingBudget;
	/**项目支出预算总额（不含税，元）*/
	@Excel(name = "项目支出预算总额（不含税，元）", width = 15)
	@Schema(description = "项目支出预算总额（不含税，元）")
    private java.math.BigDecimal projectExpenditureBudgetTotal;
	/**间接费预算总额（元）*/
	@Excel(name = "间接费预算总额（元）", width = 15)
	@Schema(description = "间接费预算总额（元）")
    private java.math.BigDecimal indirectCostBudgetTotal;
	/**项目边际利润（元）*/
	@Excel(name = "项目边际利润（元）", width = 15)
	@Schema(description = "项目边际利润（元）")
    private java.math.BigDecimal projectMarginalProfit;
	/**项目边际利润率（%）*/
	@Excel(name = "项目边际利润率（%）", width = 15)
	@Schema(description = "项目边际利润率（%）")
    private java.math.BigDecimal projectMarginalProfitRate;
	/**项目净利润（元）*/
	@Excel(name = "项目净利润（元）", width = 15)
	@Schema(description = "项目净利润（元）")
    private java.math.BigDecimal projectNetProfit;
	/**项目净利润率（%）*/
	@Excel(name = "项目净利润率（%）", width = 15)
	@Schema(description = "项目净利润率（%）")
    private java.math.BigDecimal projectNetProfitRate;
	/**工作流实例ID*/
	@Excel(name = "工作流实例ID", width = 15)
	@Schema(description = "工作流实例ID")
    private String wiid;

	@Schema(description = "附件url")
	private String  attachmentUrl;
	/**备注*/
	@Excel(name = "备注", width = 15)
	@Schema(description = "备注")
    private String remark;
	/**创建时间*/
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
	@Schema(description = "创建时间")
    private Date createTime;
	/**创建人*/
	@Schema(description = "创建人")
    private String createBy;
	/**更新时间*/
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
	@Schema(description = "更新时间")
    private Date updateTime;
	/**更新人*/
	@Schema(description = "更新人")
    private String updateBy;
	/**租户ID*/
	@Excel(name = "租户ID", width = 15)
	@Schema(description = "租户ID")
    private Integer tenantId;
	/**删除标识 0:未删除 1:删除*/
	@Excel(name = "删除标识 0:未删除 1:删除", width = 15)
	@Schema(description = "删除标识 0:未删除 1:删除")
    private Integer delFlag;
	/**所属部门代码*/
	@Schema(description = "所属部门代码")
    private String sysOrgCode;

	@ExcelCollection(name="采办包明细表")
	@Schema(description = "采办包明细表")
	private List<CostQuarterlyBudgetProcPkgDetailEntity> costQuarterlyBudgetProcPkgDetailList;
	@ExcelCollection(name="原材料明细表")
	@Schema(description = "原材料明细表")
	private List<CostQuarterlyBudgetMaterialDetailEntity> costQuarterlyBudgetMaterialDetailList;
	@ExcelCollection(name="预算科目明细直接成本表")
	@Schema(description = "预算科目明细直接成本表")
	private List<CostQuarterlyBudgetSubjectDirectCostEntity> costQuarterlyBudgetSubjectDirectCostList;
	@ExcelCollection(name="季度预算-预算科目明细本中心间接成本表")
	@Schema(description = "季度预算-预算科目明细本中心间接成本表")
	private List<CostQuarterlyBudgetCenterIndirectCostEntity> costQuarterlyBudgetCenterIndirectCostList;
	@ExcelCollection(name="季度预算-预算科目明细综合管理间接成本表")
	@Schema(description = "季度预算-预算科目明细综合管理间接成本表")
	private List<CostQuarterlyBudgetCompMageIndirectCostEntity> costQuarterlyBudgetCompMageIndirectCostList;
	@ExcelCollection(name="季度预算-预算科目明细非经营中心间接成本表")
	@Schema(description = "季度预算-预算科目明细非经营中心间接成本表")
	private List<CostQuarterlyBudgetNonOptCenterIndirectCostEntity> costQuarterlyBudgetNonOptCenterIndirectCostList;
	@ExcelCollection(name="季度预算收入明细表")
	@Schema(description = "季度预算收入明细表")
	private List<CostQuarterlyBudgetRevenueDetailEntity> costQuarterlyBudgetRevenueDetailList;

}
