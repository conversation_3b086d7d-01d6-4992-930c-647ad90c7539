package com.cdkit.modules.cm.domain.budget.service;

import com.cdkit.modules.cm.domain.budget.mode.entity.CostQuarterlyBudgetEntity;
import java.util.List;

/**
 * 季度预算提交领域服务接口
 * 负责季度预算的提交业务逻辑
 * 
 * <AUTHOR>
 * @date 2025-08-20
 */
public interface QuarterlyBudgetSubmitService {

    /**
     * 提交季度预算
     * 支持单条提交和批量提交
     * 
     * @param submitRequest 提交请求
     * @return 提交结果
     */
    SubmitResult submitQuarterlyBudgets(SubmitRequest submitRequest);

    /**
     * 提交请求
     */
    class SubmitRequest {
        /**
         * 预算ID列表（批量提交已保存记录时使用）
         */
        private List<String> budgetIds;

        /**
         * 预算数据（新增提交时使用）
         */
        private CostQuarterlyBudgetEntity budgetData;

        /**
         * 提交备注
         */
        private String submitRemark;

        /**
         * 是否强制提交
         */
        private Boolean forceSubmit;

        public SubmitRequest(List<String> budgetIds, CostQuarterlyBudgetEntity budgetData, String submitRemark, Boolean forceSubmit) {
            this.budgetIds = budgetIds;
            this.budgetData = budgetData;
            this.submitRemark = submitRemark;
            this.forceSubmit = forceSubmit != null ? forceSubmit : false;
        }

        // Getters
        public List<String> getBudgetIds() { return budgetIds; }
        public CostQuarterlyBudgetEntity getBudgetData() { return budgetData; }
        public String getSubmitRemark() { return submitRemark; }
        public Boolean getForceSubmit() { return forceSubmit; }

        public int getBudgetCount() {
            if (isNewSubmit()) return 1;
            return budgetIds != null ? budgetIds.size() : 0;
        }

        public boolean isNewSubmit() { return budgetData != null; }
        public boolean isSingleSubmit() { return budgetIds != null && budgetIds.size() == 1; }
        public boolean isBatchSubmit() { return budgetIds != null && budgetIds.size() > 1; }
    }

    /**
     * 提交结果
     */
    class SubmitResult {
        /**
         * 提交成功的数量
         */
        private Integer successCount;

        /**
         * 提交失败的数量
         */
        private Integer failureCount;

        /**
         * 总提交数量
         */
        private Integer totalCount;

        /**
         * 提交成功的预算ID列表
         */
        private List<String> successBudgetIds;

        /**
         * 提交失败的详细信息
         */
        private List<SubmitFailureDetail> failureDetails;

        /**
         * 整体提交结果消息
         */
        private String message;

        public SubmitResult(Integer successCount, Integer failureCount, Integer totalCount,
                           List<String> successBudgetIds, List<SubmitFailureDetail> failureDetails, String message) {
            this.successCount = successCount;
            this.failureCount = failureCount;
            this.totalCount = totalCount;
            this.successBudgetIds = successBudgetIds;
            this.failureDetails = failureDetails;
            this.message = message;
        }

        // Getters
        public Integer getSuccessCount() { return successCount; }
        public Integer getFailureCount() { return failureCount; }
        public Integer getTotalCount() { return totalCount; }
        public List<String> getSuccessBudgetIds() { return successBudgetIds; }
        public List<SubmitFailureDetail> getFailureDetails() { return failureDetails; }
        public String getMessage() { return message; }

        /**
         * 是否全部成功
         */
        public boolean isAllSuccess() {
            return failureCount == 0;
        }

        /**
         * 是否部分成功
         */
        public boolean isPartialSuccess() {
            return successCount > 0 && failureCount > 0;
        }

        /**
         * 是否全部失败
         */
        public boolean isAllFailure() {
            return successCount == 0;
        }
    }

    /**
     * 提交失败详细信息
     */
    class SubmitFailureDetail {
        /**
         * 预算ID
         */
        private String budgetId;

        /**
         * 预算单号
         */
        private String budgetNo;

        /**
         * 预算名称
         */
        private String budgetName;

        /**
         * 当前状态
         */
        private String currentStatus;

        /**
         * 失败原因
         */
        private String failureReason;

        public SubmitFailureDetail(String budgetId, String budgetNo, String budgetName, 
                                  String currentStatus, String failureReason) {
            this.budgetId = budgetId;
            this.budgetNo = budgetNo;
            this.budgetName = budgetName;
            this.currentStatus = currentStatus;
            this.failureReason = failureReason;
        }

        // Getters
        public String getBudgetId() { return budgetId; }
        public String getBudgetNo() { return budgetNo; }
        public String getBudgetName() { return budgetName; }
        public String getCurrentStatus() { return currentStatus; }
        public String getFailureReason() { return failureReason; }
    }
}
