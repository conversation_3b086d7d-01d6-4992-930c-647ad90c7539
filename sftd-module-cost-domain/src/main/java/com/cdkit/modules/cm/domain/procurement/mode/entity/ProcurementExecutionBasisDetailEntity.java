package com.cdkit.modules.cm.domain.procurement.mode.entity;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 采购执行依据明细表实体
 * 
 * <AUTHOR>
 * @date 2025-08-21
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ProcurementExecutionBasisDetailEntity {

    /**
     * 主键ID
     */
    private String id;

    /**
     * 关联采购执行依据主表ID
     */
    private String executionBasisId;

    /**
     * 关联季度预算主表ID
     */
    private String quarterlyBudgetId;

    /**
     * 预算编号
     */
    private String quarterlyBudgetNo;

    /**
     * 预算名称
     */
    private String quarterlyBudgetName;

    /**
     * 需求量
     */
    private BigDecimal demandQuantity;

    /**
     * 计量单位
     */
    private String unit;

    /**
     * 不含税单价
     */
    private BigDecimal unitPriceExcludingTax;

    /**
     * 不含税总价
     */
    private BigDecimal totalPriceExcludingTax;

    /**
     * 含税总价
     */
    private BigDecimal totalPriceIncludingTax;

    /**
     * 已采购量
     */
    private BigDecimal purchasedQuantity;

    /**
     * 已支出不含税金额
     */
    private BigDecimal spentAmountExcludingTax;

    /**
     * 已支出含税金额
     */
    private BigDecimal spentAmountIncludingTax;

    /**
     * 剩余不含税金额
     */
    private BigDecimal remainingAmountExcludingTax;

    /**
     * 剩余含税金额
     */
    private BigDecimal remainingAmountIncludingTax;

    /**
     * 物料编码
     */
    private String materialCode;

    /**
     * 物料名称
     */
    private String materialName;

    /**
     * 编制依据
     */
    private String compilationBasis;

    /**
     * 备注
     */
    private String remark;

    /**
     * 租户ID
     */
    private Integer tenantId;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 创建人
     */
    private String createBy;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;

    /**
     * 更新人
     */
    private String updateBy;

    /**
     * 删除标识
     */
    private Integer delFlag;

    /**
     * 所属部门代码
     */
    private String sysOrgCode;

    /**
     * 计算含税总价（假设税率13%）
     */
    public void calculateIncludingTaxPrice() {
        if (this.totalPriceExcludingTax != null) {
            this.totalPriceIncludingTax = this.totalPriceExcludingTax.multiply(new BigDecimal("1.13"));
        }
    }

    /**
     * 计算剩余金额
     */
    public void calculateRemainingAmount() {
        if (this.totalPriceExcludingTax != null && this.spentAmountExcludingTax != null) {
            this.remainingAmountExcludingTax = this.totalPriceExcludingTax.subtract(this.spentAmountExcludingTax);
        }
        if (this.totalPriceIncludingTax != null && this.spentAmountIncludingTax != null) {
            this.remainingAmountIncludingTax = this.totalPriceIncludingTax.subtract(this.spentAmountIncludingTax);
        }
    }

    /**
     * 初始化默认值
     */
    public void initializeDefaults() {
        if (this.purchasedQuantity == null) {
            this.purchasedQuantity = BigDecimal.ZERO;
        }
        if (this.spentAmountExcludingTax == null) {
            this.spentAmountExcludingTax = BigDecimal.ZERO;
        }
        if (this.spentAmountIncludingTax == null) {
            this.spentAmountIncludingTax = BigDecimal.ZERO;
        }
        if (this.delFlag == null) {
            this.delFlag = 0;
        }
        
        calculateIncludingTaxPrice();
        calculateRemainingAmount();
    }
}
