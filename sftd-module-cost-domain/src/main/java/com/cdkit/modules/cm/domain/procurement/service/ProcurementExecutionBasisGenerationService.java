package com.cdkit.modules.cm.domain.procurement.service;

/**
 * 采购执行依据生成领域服务接口
 * 用于处理季度预算锁定后自动生成采购执行依据的业务逻辑
 * 
 * <AUTHOR>
 * @date 2025-08-21
 */
public interface ProcurementExecutionBasisGenerationService {

    /**
     * 根据季度预算生成采购执行依据
     * 当季度预算锁定后，自动提取物料需求数据并按照业务规则合并生成采购执行依据
     * 
     * @param quarterlyBudgetId 季度预算ID
     * @param tenantId 租户ID
     * @param operatorBy 操作人
     * @return 生成结果
     */
    GenerationResult generateFromQuarterlyBudget(String quarterlyBudgetId, Integer tenantId, String operatorBy);

    /**
     * 批量生成采购执行依据
     * 用于处理多个季度预算同时锁定的场景
     * 
     * @param quarterlyBudgetIds 季度预算ID列表
     * @param tenantId 租户ID
     * @param operatorBy 操作人
     * @return 批量生成结果
     */
    BatchGenerationResult batchGenerateFromQuarterlyBudgets(java.util.List<String> quarterlyBudgetIds, 
                                                           Integer tenantId, String operatorBy);

    /**
     * 重新生成采购执行依据
     * 用于处理季度预算变更后需要重新生成执行依据的场景
     * 
     * @param quarterlyBudgetId 季度预算ID
     * @param tenantId 租户ID
     * @param operatorBy 操作人
     * @return 重新生成结果
     */
    GenerationResult regenerateFromQuarterlyBudget(String quarterlyBudgetId, Integer tenantId, String operatorBy);

    /**
     * 生成结果
     */
    class GenerationResult {
        /**
         * 是否成功
         */
        private final boolean success;

        /**
         * 生成的主表记录数
         */
        private final int mainRecordCount;

        /**
         * 生成的明细记录数
         */
        private final int detailRecordCount;

        /**
         * 处理的物料数量
         */
        private final int materialCount;

        /**
         * 错误信息
         */
        private final String errorMessage;

        /**
         * 处理耗时（毫秒）
         */
        private final long processingTimeMs;

        public GenerationResult(boolean success, int mainRecordCount, int detailRecordCount, 
                              int materialCount, String errorMessage, long processingTimeMs) {
            this.success = success;
            this.mainRecordCount = mainRecordCount;
            this.detailRecordCount = detailRecordCount;
            this.materialCount = materialCount;
            this.errorMessage = errorMessage;
            this.processingTimeMs = processingTimeMs;
        }

        public static GenerationResult success(int mainRecordCount, int detailRecordCount, 
                                             int materialCount, long processingTimeMs) {
            return new GenerationResult(true, mainRecordCount, detailRecordCount, 
                                      materialCount, null, processingTimeMs);
        }

        public static GenerationResult failure(String errorMessage, long processingTimeMs) {
            return new GenerationResult(false, 0, 0, 0, errorMessage, processingTimeMs);
        }

        // Getters
        public boolean isSuccess() { return success; }
        public int getMainRecordCount() { return mainRecordCount; }
        public int getDetailRecordCount() { return detailRecordCount; }
        public int getMaterialCount() { return materialCount; }
        public String getErrorMessage() { return errorMessage; }
        public long getProcessingTimeMs() { return processingTimeMs; }

        @Override
        public String toString() {
            if (success) {
                return String.format("生成成功: 主表%d条, 明细%d条, 物料%d种, 耗时%dms", 
                                   mainRecordCount, detailRecordCount, materialCount, processingTimeMs);
            } else {
                return String.format("生成失败: %s, 耗时%dms", errorMessage, processingTimeMs);
            }
        }
    }

    /**
     * 批量生成结果
     */
    class BatchGenerationResult {
        /**
         * 总处理数量
         */
        private final int totalCount;

        /**
         * 成功数量
         */
        private final int successCount;

        /**
         * 失败数量
         */
        private final int failureCount;

        /**
         * 生成的主表记录总数
         */
        private final int totalMainRecordCount;

        /**
         * 生成的明细记录总数
         */
        private final int totalDetailRecordCount;

        /**
         * 失败详情
         */
        private final java.util.List<String> failureDetails;

        /**
         * 总处理耗时（毫秒）
         */
        private final long totalProcessingTimeMs;

        public BatchGenerationResult(int totalCount, int successCount, int failureCount,
                                   int totalMainRecordCount, int totalDetailRecordCount,
                                   java.util.List<String> failureDetails, long totalProcessingTimeMs) {
            this.totalCount = totalCount;
            this.successCount = successCount;
            this.failureCount = failureCount;
            this.totalMainRecordCount = totalMainRecordCount;
            this.totalDetailRecordCount = totalDetailRecordCount;
            this.failureDetails = failureDetails;
            this.totalProcessingTimeMs = totalProcessingTimeMs;
        }

        // Getters
        public int getTotalCount() { return totalCount; }
        public int getSuccessCount() { return successCount; }
        public int getFailureCount() { return failureCount; }
        public int getTotalMainRecordCount() { return totalMainRecordCount; }
        public int getTotalDetailRecordCount() { return totalDetailRecordCount; }
        public java.util.List<String> getFailureDetails() { return failureDetails; }
        public long getTotalProcessingTimeMs() { return totalProcessingTimeMs; }

        public boolean isAllSuccess() {
            return failureCount == 0 && totalCount > 0;
        }

        @Override
        public String toString() {
            return String.format("批量生成完成: 总数%d, 成功%d, 失败%d, 主表%d条, 明细%d条, 耗时%dms", 
                               totalCount, successCount, failureCount, 
                               totalMainRecordCount, totalDetailRecordCount, totalProcessingTimeMs);
        }
    }
}
