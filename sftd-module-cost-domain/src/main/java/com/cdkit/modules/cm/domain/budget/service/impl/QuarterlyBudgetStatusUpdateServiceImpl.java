package com.cdkit.modules.cm.domain.budget.service.impl;

import com.cdkit.modules.cm.domain.budget.event.QuarterlyBudgetLockedEvent;
import com.cdkit.modules.cm.domain.budget.mode.entity.CostQuarterlyBudgetEntity;
import com.cdkit.modules.cm.domain.budget.repository.CostQuarterlyBudgetRepository;
import com.cdkit.modules.cm.domain.budget.service.QuarterlyBudgetStatusUpdateService;
import com.cdkit.modules.cm.domain.budget.valobj.BudgetStatusEnum;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import java.util.List;

/**
 * 季度预算状态更新领域服务实现
 * 
 * <AUTHOR>
 * @date 2025-08-21
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class QuarterlyBudgetStatusUpdateServiceImpl implements QuarterlyBudgetStatusUpdateService {

    private final CostQuarterlyBudgetRepository costQuarterlyBudgetRepository;
    private final ApplicationEventPublisher applicationEventPublisher;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateBudgetStatusBatch(List<String> budgetIds, String newStatus, String operatorBy) {
        log.info("开始批量更新季度预算状态，预算数量: {}, 新状态: {}, 操作人: {}", 
                budgetIds.size(), newStatus, operatorBy);

        // 参数校验
        if (budgetIds == null || budgetIds.isEmpty()) {
            throw new IllegalArgumentException("预算ID列表不能为空");
        }

        if (!StringUtils.hasText(newStatus)) {
            throw new IllegalArgumentException("新状态不能为空");
        }

        try {
            // 如果新状态是LOCKED，需要先查询预算信息用于发布事件
            List<CostQuarterlyBudgetEntity> budgetEntities = null;
            if (BudgetStatusEnum.LOCKED.getCode().equals(newStatus)) {
                budgetEntities = costQuarterlyBudgetRepository.findByIds(budgetIds);
                log.info("查询到{}条预算记录用于发布锁定事件", budgetEntities.size());
            }

            // 执行状态更新
            costQuarterlyBudgetRepository.updateBudgetStatusBatch(budgetIds, newStatus);
            log.info("批量更新季度预算状态完成，更新数量: {}, 新状态: {}", budgetIds.size(), newStatus);

            // 如果状态更新为LOCKED，发布锁定事件
            if (BudgetStatusEnum.LOCKED.getCode().equals(newStatus) && budgetEntities != null) {
                publishBudgetLockedEvents(budgetEntities, operatorBy);
            }

        } catch (Exception e) {
            log.error("批量更新季度预算状态失败，预算数量: {}, 新状态: {}", budgetIds.size(), newStatus, e);
            throw new RuntimeException("批量更新预算状态失败: " + e.getMessage(), e);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateBudgetStatus(String budgetId, String newStatus, String operatorBy) {
        log.info("开始更新季度预算状态，预算ID: {}, 新状态: {}, 操作人: {}", budgetId, newStatus, operatorBy);

        // 参数校验
        if (!StringUtils.hasText(budgetId)) {
            throw new IllegalArgumentException("预算ID不能为空");
        }

        if (!StringUtils.hasText(newStatus)) {
            throw new IllegalArgumentException("新状态不能为空");
        }

        try {
            // 如果新状态是LOCKED，需要先查询预算信息用于发布事件
            CostQuarterlyBudgetEntity budgetEntity = null;
            if (BudgetStatusEnum.LOCKED.getCode().equals(newStatus)) {
                budgetEntity = costQuarterlyBudgetRepository.findById(budgetId);
                if (budgetEntity == null) {
                    throw new IllegalArgumentException("季度预算不存在，ID: " + budgetId);
                }
            }

            // 执行状态更新
            updateBudgetStatusBatch(List.of(budgetId), newStatus, operatorBy);

            // 如果状态更新为LOCKED，发布锁定事件
            if (BudgetStatusEnum.LOCKED.getCode().equals(newStatus) && budgetEntity != null) {
                publishBudgetLockedEvent(budgetEntity, operatorBy);
            }

        } catch (Exception e) {
            log.error("更新季度预算状态失败，预算ID: {}, 新状态: {}", budgetId, newStatus, e);
            throw new RuntimeException("更新预算状态失败: " + e.getMessage(), e);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void approveBudgets(List<String> budgetIds, String operatorBy) {
        log.info("开始审批通过季度预算，预算数量: {}, 操作人: {}", budgetIds.size(), operatorBy);
        
        updateBudgetStatusBatch(budgetIds, BudgetStatusEnum.getApprovedStatus(), operatorBy);
        
        log.info("审批通过季度预算完成，预算数量: {}", budgetIds.size());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void rejectBudgets(List<String> budgetIds, String operatorBy, String rejectReason) {
        log.info("开始审批驳回季度预算，预算数量: {}, 操作人: {}, 驳回原因: {}", 
                budgetIds.size(), operatorBy, rejectReason);
        
        updateBudgetStatusBatch(budgetIds, BudgetStatusEnum.getRejectedStatus(), operatorBy);
        
        // TODO: 可以在这里记录驳回原因到审批日志表
        log.info("审批驳回季度预算完成，预算数量: {}", budgetIds.size());
    }

    /**
     * 发布预算锁定事件
     */
    private void publishBudgetLockedEvent(CostQuarterlyBudgetEntity budgetEntity, String operatorBy) {
        try {
            QuarterlyBudgetLockedEvent event = new QuarterlyBudgetLockedEvent(
                    budgetEntity.getId(),
                    budgetEntity.getTenantId(),
                    budgetEntity.getQuarterlyBudgetNo(),
                    budgetEntity.getQuarterlyBudgetName(),
                    operatorBy
            );

            applicationEventPublisher.publishEvent(event);
            log.info("发布季度预算锁定事件成功: {}", event);

        } catch (Exception e) {
            log.error("发布季度预算锁定事件失败，预算ID: {}", budgetEntity.getId(), e);
            // 事件发布失败不影响主业务流程，只记录错误日志
        }
    }

    /**
     * 批量发布预算锁定事件
     */
    private void publishBudgetLockedEvents(List<CostQuarterlyBudgetEntity> budgetEntities, String operatorBy) {
        int eventCount = 0;
        for (CostQuarterlyBudgetEntity budgetEntity : budgetEntities) {
            try {
                publishBudgetLockedEvent(budgetEntity, operatorBy);
                eventCount++;
            } catch (Exception e) {
                log.error("发布季度预算锁定事件失败，预算ID: {}", budgetEntity.getId(), e);
                // 继续处理其他事件
            }
        }
        log.info("批量发布季度预算锁定事件完成，成功发布: {}/{}", eventCount, budgetEntities.size());
    }
}
