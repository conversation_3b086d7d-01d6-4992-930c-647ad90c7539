package com.cdkit.modules.cm.domain.procurement.repository;

import com.cdkit.common.page.PageReq;
import com.cdkit.common.page.PageRes;
import com.cdkit.modules.cm.domain.procurement.entity.ProcurementApplicationEntity;

import java.util.List;

/**
 * 采购申请仓储接口
 * 
 * <AUTHOR>
 * @date 2025-08-26
 */
public interface ProcurementApplicationRepository {

    /**
     * 分页查询采购申请列表
     *
     * @param queryEntity 查询条件
     * @param pageReq 分页参数
     * @return 分页结果
     */
    PageRes<ProcurementApplicationEntity> queryPageList(ProcurementApplicationEntity queryEntity, PageReq pageReq);

    /**
     * 保存采购申请（包含明细）
     *
     * @param entity 采购申请实体
     * @return 保存后的实体
     */
    ProcurementApplicationEntity save(ProcurementApplicationEntity entity);

    /**
     * 更新采购申请（包含明细）
     * 
     * @param entity 采购申请实体
     * @return 更新后的实体
     */
    ProcurementApplicationEntity update(ProcurementApplicationEntity entity);

    /**
     * 根据ID查询采购申请详情（包含明细）
     * 
     * @param id 采购申请ID
     * @return 采购申请实体
     */
    ProcurementApplicationEntity getById(String id);

    /**
     * 根据ID删除采购申请
     * 
     * @param id 采购申请ID
     */
    void deleteById(String id);

    /**
     * 批量删除采购申请
     * 
     * @param ids 采购申请ID列表
     */
    void deleteBatch(List<String> ids);

    /**
     * 生成采购申请单号
     * 格式：PA + YYYYMMDD + 4位序号
     * 
     * @return 采购申请单号
     */
    String generateApplicationNo();

    /**
     * 根据申请单号查询采购申请
     * 
     * @param applicationNo 申请单号
     * @return 采购申请实体
     */
    ProcurementApplicationEntity findByApplicationNo(String applicationNo);

    /**
     * 检查申请单号是否存在
     *
     * @param applicationNo 申请单号
     * @return 是否存在
     */
    boolean existsByApplicationNo(String applicationNo);

    /**
     * 更新采购执行依据的已采购量
     * 根据采购申请的信息更新对应的采购执行依据主表和明细表的已采购量
     *
     * @param entity 采购申请实体
     */
    void updateExecutionBasisPurchasedQuantity(ProcurementApplicationEntity entity);

    /**
     * 查询所有符合条件的主表数据（不包含明细数据）
     * 用于导出功能
     *
     * @param queryEntity 查询条件
     * @return 主表数据列表
     */
    List<ProcurementApplicationEntity> findAllMainTable(ProcurementApplicationEntity queryEntity);

    /**
     * 根据差量更新采购执行依据的已采购量和已支出金额
     * 用于编辑采购申请时，避免重复累计或丢失历史数据
     *
     * @param originalEntity 原采购申请实体
     * @param newEntity 新采购申请实体
     */
    void updateExecutionBasisPurchasedQuantityByDelta(ProcurementApplicationEntity originalEntity,
                                                      ProcurementApplicationEntity newEntity);
}
