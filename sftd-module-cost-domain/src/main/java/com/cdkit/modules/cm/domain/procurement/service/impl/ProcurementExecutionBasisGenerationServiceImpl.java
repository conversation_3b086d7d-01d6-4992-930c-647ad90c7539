package com.cdkit.modules.cm.domain.procurement.service.impl;

import com.cdkit.modules.cm.domain.budget.repository.CostQuarterlyBudgetRepository;
import com.cdkit.modules.cm.domain.procurement.mode.entity.ProcurementExecutionBasisDetailEntity;
import com.cdkit.modules.cm.domain.procurement.mode.entity.ProcurementExecutionBasisEntity;
import com.cdkit.modules.cm.domain.procurement.mode.valobj.MaterialDemandData;
import com.cdkit.modules.cm.domain.procurement.repository.ProcurementExecutionBasisRepository;
import com.cdkit.modules.cm.domain.procurement.service.ProcurementExecutionBasisGenerationService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 采购执行依据生成领域服务实现
 * 
 * <AUTHOR>
 * @date 2025-08-21
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class ProcurementExecutionBasisGenerationServiceImpl implements ProcurementExecutionBasisGenerationService {

    private final CostQuarterlyBudgetRepository quarterlyBudgetRepository;
    private final ProcurementExecutionBasisRepository procurementExecutionBasisRepository;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public GenerationResult generateFromQuarterlyBudget(String quarterlyBudgetId, Integer tenantId, String operatorBy) {
        long startTime = System.currentTimeMillis();

        log.info("开始生成采购执行依据，季度预算ID: {}, 租户ID: {}, 操作人: {}",
                quarterlyBudgetId, tenantId, operatorBy);

        // 参数校验
        if (!StringUtils.hasText(quarterlyBudgetId)) {
            String errorMsg = "季度预算ID不能为空";
            log.error(errorMsg);
            return GenerationResult.failure(errorMsg, System.currentTimeMillis() - startTime);
        }

        if (tenantId == null) {
            String errorMsg = "租户ID不能为空";
            log.error(errorMsg);
            return GenerationResult.failure(errorMsg, System.currentTimeMillis() - startTime);
        }

        try {
            // 1. 查询物料需求数据
            List<MaterialDemandData> materialDemands = queryMaterialDemandData(quarterlyBudgetId, tenantId);
            if (materialDemands.isEmpty()) {
                log.warn("未找到物料需求数据，季度预算ID: {}", quarterlyBudgetId);
                return GenerationResult.success(0, 0, 0, System.currentTimeMillis() - startTime);
            }

            // 2. 按物料和时间周期分组合并
            Map<String, List<MaterialDemandData>> groupedDemands = groupMaterialDemands(materialDemands);

            // 3. 生成采购执行依据数据
            List<ProcurementExecutionBasisEntity> executionBasisList = generateExecutionBasisEntities(
                    groupedDemands, operatorBy, tenantId);

            // 4. 保存数据
            saveExecutionBasisData(executionBasisList);

            // 5. 统计结果
            int mainRecordCount = executionBasisList.size();
            int detailRecordCount = executionBasisList.stream()
                    .mapToInt(entity -> entity.getDetails() != null ? entity.getDetails().size() : 0)
                    .sum();
            int materialCount = (int) executionBasisList.stream()
                    .map(ProcurementExecutionBasisEntity::getMaterialCode)
                    .distinct()
                    .count();

            long processingTime = System.currentTimeMillis() - startTime;

            log.info("生成采购执行依据完成，季度预算ID: {}, 主表记录: {}, 明细记录: {}, 物料数量: {}, 耗时: {}ms",
                    quarterlyBudgetId, mainRecordCount, detailRecordCount, materialCount, processingTime);

            return GenerationResult.success(mainRecordCount, detailRecordCount, materialCount, processingTime);

        } catch (Exception e) {
            long processingTime = System.currentTimeMillis() - startTime;
            String errorMsg = "生成采购执行依据失败: " + e.getMessage();
            log.error("生成采购执行依据失败，季度预算ID: {}", quarterlyBudgetId, e);
            return GenerationResult.failure(errorMsg, processingTime);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public BatchGenerationResult batchGenerateFromQuarterlyBudgets(List<String> quarterlyBudgetIds, 
                                                                  Integer tenantId, String operatorBy) {
        long startTime = System.currentTimeMillis();
        
        log.info("开始批量生成采购执行依据，预算数量: {}, 租户ID: {}, 操作人: {}", 
                quarterlyBudgetIds.size(), tenantId, operatorBy);

        if (quarterlyBudgetIds == null || quarterlyBudgetIds.isEmpty()) {
            String errorMsg = "季度预算ID列表不能为空";
            log.error(errorMsg);
            return new BatchGenerationResult(0, 0, 0, 0, 0, 
                    List.of(errorMsg), System.currentTimeMillis() - startTime);
        }

        int totalCount = quarterlyBudgetIds.size();
        int successCount = 0;
        int failureCount = 0;
        int totalMainRecordCount = 0;
        int totalDetailRecordCount = 0;
        List<String> failureDetails = new ArrayList<>();

        for (String budgetId : quarterlyBudgetIds) {
            try {
                GenerationResult result = generateFromQuarterlyBudget(budgetId, tenantId, operatorBy);
                if (result.isSuccess()) {
                    successCount++;
                    totalMainRecordCount += result.getMainRecordCount();
                    totalDetailRecordCount += result.getDetailRecordCount();
                } else {
                    failureCount++;
                    failureDetails.add(String.format("预算ID[%s]: %s", budgetId, result.getErrorMessage()));
                }
            } catch (Exception e) {
                failureCount++;
                failureDetails.add(String.format("预算ID[%s]: %s", budgetId, e.getMessage()));
                log.error("批量生成采购执行依据失败，预算ID: {}", budgetId, e);
            }
        }

        long totalProcessingTime = System.currentTimeMillis() - startTime;
        
        log.info("批量生成采购执行依据完成，总数: {}, 成功: {}, 失败: {}, 主表: {}, 明细: {}, 耗时: {}ms", 
                totalCount, successCount, failureCount, totalMainRecordCount, totalDetailRecordCount, totalProcessingTime);

        return new BatchGenerationResult(totalCount, successCount, failureCount,
                totalMainRecordCount, totalDetailRecordCount, failureDetails, totalProcessingTime);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public GenerationResult regenerateFromQuarterlyBudget(String quarterlyBudgetId, Integer tenantId, String operatorBy) {
        long startTime = System.currentTimeMillis();
        
        log.info("开始重新生成采购执行依据，季度预算ID: {}, 租户ID: {}, 操作人: {}", 
                quarterlyBudgetId, tenantId, operatorBy);

        try {
            // 先删除现有的采购执行依据数据
            deleteExistingExecutionBasis(quarterlyBudgetId, tenantId);
            
            // 重新生成
            return generateFromQuarterlyBudget(quarterlyBudgetId, tenantId, operatorBy);

        } catch (Exception e) {
            long processingTime = System.currentTimeMillis() - startTime;
            String errorMsg = "重新生成采购执行依据失败: " + e.getMessage();
            log.error("重新生成采购执行依据失败，季度预算ID: {}", quarterlyBudgetId, e);
            return GenerationResult.failure(errorMsg, processingTime);
        }
    }

    /**
     * 查询物料需求数据
     */
    private List<MaterialDemandData> queryMaterialDemandData(String quarterlyBudgetId, Integer tenantId) {
        // 通过仓储接口查询物料需求数据
        return quarterlyBudgetRepository.findMaterialDemandsByBudgetId(quarterlyBudgetId, tenantId);
    }

    /**
     * 按物料和时间周期分组合并需求数据
     */
    private Map<String, List<MaterialDemandData>> groupMaterialDemands(List<MaterialDemandData> materialDemands) {
        return materialDemands.stream()
                .filter(MaterialDemandData::isValid)
                .collect(Collectors.groupingBy(MaterialDemandData::createGroupKey));
    }

    /**
     * 生成采购执行依据实体列表
     */
    private List<ProcurementExecutionBasisEntity> generateExecutionBasisEntities(
            Map<String, List<MaterialDemandData>> groupedDemands, String operatorBy, Integer tenantId) {

        List<ProcurementExecutionBasisEntity> executionBasisList = new ArrayList<>();

        for (Map.Entry<String, List<MaterialDemandData>> entry : groupedDemands.entrySet()) {
            List<MaterialDemandData> demands = entry.getValue();
            if (demands.isEmpty()) {
                continue;
            }

            // 取第一个作为基础信息（同一组的物料编码、时间周期相同）
            MaterialDemandData firstDemand = demands.get(0);

            // 创建主表实体
            ProcurementExecutionBasisEntity mainEntity = createMainEntity(demands, firstDemand, operatorBy, tenantId);

            // 创建明细实体列表
            List<ProcurementExecutionBasisDetailEntity> detailEntities = createDetailEntities(
                    demands, mainEntity.getId(), operatorBy, tenantId);

            mainEntity.setDetails(detailEntities);
            executionBasisList.add(mainEntity);
        }

        return executionBasisList;
    }

    /**
     * 创建主表实体
     */
    private ProcurementExecutionBasisEntity createMainEntity(List<MaterialDemandData> demands,
                                                           MaterialDemandData baseDemand,
                                                           String operatorBy, Integer tenantId) {
        // 计算汇总数据
        BigDecimal totalDemandQuantity = demands.stream()
                .map(MaterialDemandData::getUsageAmount)
                .reduce(BigDecimal.ZERO, BigDecimal::add);

        BigDecimal totalPriceExcludingTax = demands.stream()
                .map(MaterialDemandData::getTotalPriceExcludingTax)
                .reduce(BigDecimal.ZERO, BigDecimal::add);

        // 计算加权平均单价
        BigDecimal avgUnitPrice = totalDemandQuantity.compareTo(BigDecimal.ZERO) > 0
                ? totalPriceExcludingTax.divide(totalDemandQuantity, 6, BigDecimal.ROUND_HALF_UP)
                : BigDecimal.ZERO;

        // 创建主表实体
        ProcurementExecutionBasisEntity entity = ProcurementExecutionBasisEntity.builder()
                .id(generateId())
                .materialCode(baseDemand.getMaterialCode())
                .materialName(baseDemand.getMaterialName())
                .unit(baseDemand.getUnit())
                .quarter(baseDemand.getQuarter())
                .startDate(baseDemand.getStartDate())
                .endDate(baseDemand.getEndDate())
                .totalDemandQuantity(totalDemandQuantity)
                .unitPriceExcludingTax(avgUnitPrice)
                .totalPriceExcludingTax(totalPriceExcludingTax)
                .sourceQuarterlyBudgetCount(demands.size())
                .tenantId(tenantId)
                .createTime(LocalDateTime.now())
                .createBy(operatorBy)
                .updateTime(LocalDateTime.now())
                .updateBy(operatorBy)
                .build();

        entity.initializeDefaults();
        return entity;
    }

    /**
     * 创建明细实体列表
     */
    private List<ProcurementExecutionBasisDetailEntity> createDetailEntities(List<MaterialDemandData> demands,
                                                                            String executionBasisId,
                                                                            String operatorBy, Integer tenantId) {
        List<ProcurementExecutionBasisDetailEntity> detailEntities = new ArrayList<>();

        for (MaterialDemandData demand : demands) {
            ProcurementExecutionBasisDetailEntity detailEntity = ProcurementExecutionBasisDetailEntity.builder()
                    .id(generateId())
                    .executionBasisId(executionBasisId)
                    .quarterlyBudgetId(demand.getQuarterlyBudgetId())
                    .quarterlyBudgetNo(demand.getQuarterlyBudgetNo())
                    .quarterlyBudgetName(demand.getQuarterlyBudgetName())
                    .demandQuantity(demand.getUsageAmount())
                    .unit(demand.getUnit())
                    .unitPriceExcludingTax(demand.getUnitPriceExcludingTax())
                    .totalPriceExcludingTax(demand.getTotalPriceExcludingTax())
                    .materialCode(demand.getMaterialCode())
                    .materialName(demand.getMaterialName())
                    .compilationBasis(demand.getCompilationBasis())
                    .remark(demand.getRemark())
                    .tenantId(tenantId)
                    .createTime(LocalDateTime.now())
                    .createBy(operatorBy)
                    .updateTime(LocalDateTime.now())
                    .updateBy(operatorBy)
                    .build();

            detailEntity.initializeDefaults();
            detailEntities.add(detailEntity);
        }

        return detailEntities;
    }

    /**
     * 保存采购执行依据数据
     */
    private void saveExecutionBasisData(List<ProcurementExecutionBasisEntity> executionBasisList) {
        if (executionBasisList.isEmpty()) {
            return;
        }

        // 批量保存主表数据
        procurementExecutionBasisRepository.saveBatch(executionBasisList);

        // 批量保存明细数据
        List<ProcurementExecutionBasisDetailEntity> allDetails = executionBasisList.stream()
                .filter(entity -> entity.getDetails() != null)
                .flatMap(entity -> entity.getDetails().stream())
                .collect(Collectors.toList());

        if (!allDetails.isEmpty()) {
            procurementExecutionBasisRepository.saveDetailBatch(allDetails);
        }

        log.info("保存采购执行依据数据完成，主表: {}条, 明细: {}条",
                executionBasisList.size(), allDetails.size());
    }

    /**
     * 删除现有的采购执行依据数据
     */
    private void deleteExistingExecutionBasis(String quarterlyBudgetId, Integer tenantId) {
        try {
            procurementExecutionBasisRepository.deleteByQuarterlyBudgetId(quarterlyBudgetId, tenantId);
            log.info("删除现有采购执行依据数据完成，季度预算ID: {}", quarterlyBudgetId);
        } catch (Exception e) {
            log.error("删除现有采购执行依据数据失败", e);
            throw new RuntimeException("删除现有数据失败: " + e.getMessage(), e);
        }
    }

    /**
     * 生成UUID（去掉横线）
     */
    private String generateId() {
        return UUID.randomUUID().toString().replace("-", "");
    }

    /**
     * 生成统计信息
     */
    private static class GenerationStatistics {
        final int mainRecordCount;
        final int detailRecordCount;
        final int materialCount;

        GenerationStatistics(int mainRecordCount, int detailRecordCount, int materialCount) {
            this.mainRecordCount = mainRecordCount;
            this.detailRecordCount = detailRecordCount;
            this.materialCount = materialCount;
        }
    }
}
