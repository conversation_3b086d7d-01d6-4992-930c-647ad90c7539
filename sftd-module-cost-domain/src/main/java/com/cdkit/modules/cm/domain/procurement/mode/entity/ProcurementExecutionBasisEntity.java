package com.cdkit.modules.cm.domain.procurement.mode.entity;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

/**
 * 采购执行依据主表实体
 * 
 * <AUTHOR>
 * @date 2025-08-21
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ProcurementExecutionBasisEntity {

    /**
     * 主键ID
     */
    private String id;

    /**
     * 物料编码
     */
    private String materialCode;

    /**
     * 物料名称
     */
    private String materialName;

    /**
     * 计量单位
     */
    private String unit;

    /**
     * 所在季度
     */
    private String quarter;

    /**
     * 开始时间
     */
    private LocalDate startDate;

    /**
     * 结束时间
     */
    private LocalDate endDate;

    /**
     * 总需求量
     */
    private BigDecimal totalDemandQuantity;

    /**
     * 不含税单价
     */
    private BigDecimal unitPriceExcludingTax;

    /**
     * 不含税总价
     */
    private BigDecimal totalPriceExcludingTax;

    /**
     * 含税总价
     */
    private BigDecimal totalPriceIncludingTax;

    /**
     * 已采购量
     */
    private BigDecimal purchasedQuantity;

    /**
     * 已支出不含税金额
     */
    private BigDecimal spentAmountExcludingTax;

    /**
     * 已支出含税金额
     */
    private BigDecimal spentAmountIncludingTax;

    /**
     * 剩余不含税金额
     */
    private BigDecimal remainingAmountExcludingTax;

    /**
     * 剩余含税金额
     */
    private BigDecimal remainingAmountIncludingTax;

    /**
     * 生成状态
     */
    private String generationStatus;

    /**
     * 来源季度预算数量
     */
    private Integer sourceQuarterlyBudgetCount;

    /**
     * 租户ID
     */
    private Integer tenantId;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 创建人
     */
    private String createBy;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;

    /**
     * 更新人
     */
    private String updateBy;

    /**
     * 删除标识
     */
    private Integer delFlag;

    /**
     * 所属部门代码
     */
    private String sysOrgCode;

    /**
     * 明细列表
     */
    private List<ProcurementExecutionBasisDetailEntity> details;

    /**
     * 添加明细
     */
    public void addDetail(ProcurementExecutionBasisDetailEntity detail) {
        if (this.details == null) {
            this.details = new ArrayList<>();
        }
        detail.setExecutionBasisId(this.id);
        this.details.add(detail);
    }

    /**
     * 计算含税总价（假设税率13%）
     */
    public void calculateIncludingTaxPrice() {
        if (this.totalPriceExcludingTax != null) {
            this.totalPriceIncludingTax = this.totalPriceExcludingTax.multiply(new BigDecimal("1.13"));
        }
    }

    /**
     * 计算剩余金额
     */
    public void calculateRemainingAmount() {
        if (this.totalPriceExcludingTax != null && this.spentAmountExcludingTax != null) {
            this.remainingAmountExcludingTax = this.totalPriceExcludingTax.subtract(this.spentAmountExcludingTax);
        }
        if (this.totalPriceIncludingTax != null && this.spentAmountIncludingTax != null) {
            this.remainingAmountIncludingTax = this.totalPriceIncludingTax.subtract(this.spentAmountIncludingTax);
        }
    }

    /**
     * 初始化默认值
     */
    public void initializeDefaults() {
        if (this.purchasedQuantity == null) {
            this.purchasedQuantity = BigDecimal.ZERO;
        }
        if (this.spentAmountExcludingTax == null) {
            this.spentAmountExcludingTax = BigDecimal.ZERO;
        }
        if (this.spentAmountIncludingTax == null) {
            this.spentAmountIncludingTax = BigDecimal.ZERO;
        }
        if (this.generationStatus == null) {
            this.generationStatus = "GENERATED";
        }
        if (this.delFlag == null) {
            this.delFlag = 0;
        }
        
        calculateIncludingTaxPrice();
        calculateRemainingAmount();
    }

    /**
     * 创建物料分组键
     */
    public String createMaterialGroupKey() {
        return String.format("%s_%s_%s", materialCode, startDate, endDate);
    }
}
