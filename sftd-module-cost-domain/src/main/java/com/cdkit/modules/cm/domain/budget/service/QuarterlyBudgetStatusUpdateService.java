package com.cdkit.modules.cm.domain.budget.service;

import java.util.List;

/**
 * 季度预算状态更新领域服务接口
 * 负责处理季度预算状态变更并发布相应的领域事件
 * 
 * <AUTHOR>
 * @date 2025-08-21
 */
public interface QuarterlyBudgetStatusUpdateService {

    /**
     * 批量更新季度预算状态
     * 当状态更新为LOCKED时，会自动发布QuarterlyBudgetLockedEvent事件
     * 
     * @param budgetIds 预算ID列表
     * @param newStatus 新状态
     * @param operatorBy 操作人
     */
    void updateBudgetStatusBatch(List<String> budgetIds, String newStatus, String operatorBy);

    /**
     * 更新单个季度预算状态
     * 当状态更新为LOCKED时，会自动发布QuarterlyBudgetLockedEvent事件
     * 
     * @param budgetId 预算ID
     * @param newStatus 新状态
     * @param operatorBy 操作人
     */
    void updateBudgetStatus(String budgetId, String newStatus, String operatorBy);

    /**
     * 审批通过季度预算（状态变更为LOCKED）
     * 专门用于审批流程，会发布锁定事件
     * 
     * @param budgetIds 预算ID列表
     * @param operatorBy 操作人
     */
    void approveBudgets(List<String> budgetIds, String operatorBy);

    /**
     * 审批驳回季度预算（状态变更为REJECTED）
     * 
     * @param budgetIds 预算ID列表
     * @param operatorBy 操作人
     * @param rejectReason 驳回原因
     */
    void rejectBudgets(List<String> budgetIds, String operatorBy, String rejectReason);

    /**
     * 状态更新结果
     */
    class StatusUpdateResult {
        /**
         * 是否成功
         */
        private final boolean success;

        /**
         * 更新数量
         */
        private final int updateCount;

        /**
         * 发布事件数量
         */
        private final int eventCount;

        /**
         * 错误信息
         */
        private final String errorMessage;

        public StatusUpdateResult(boolean success, int updateCount, int eventCount, String errorMessage) {
            this.success = success;
            this.updateCount = updateCount;
            this.eventCount = eventCount;
            this.errorMessage = errorMessage;
        }

        public static StatusUpdateResult success(int updateCount, int eventCount) {
            return new StatusUpdateResult(true, updateCount, eventCount, null);
        }

        public static StatusUpdateResult failure(String errorMessage) {
            return new StatusUpdateResult(false, 0, 0, errorMessage);
        }

        // Getters
        public boolean isSuccess() { return success; }
        public int getUpdateCount() { return updateCount; }
        public int getEventCount() { return eventCount; }
        public String getErrorMessage() { return errorMessage; }

        @Override
        public String toString() {
            if (success) {
                return String.format("状态更新成功: 更新%d条记录, 发布%d个事件", updateCount, eventCount);
            } else {
                return String.format("状态更新失败: %s", errorMessage);
            }
        }
    }
}
