package com.cdkit.modules.cm.domain.procurement.entity;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * 采购申请领域实体
 * 
 * <AUTHOR>
 * @date 2025-08-26
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ProcurementApplicationEntity implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * UUID主键
     */
    private String id;

    /**
     * 采购申请单号（自动生成）
     */
    private String applicationNo;

    /**
     * 物料编码
     */
    private String materialCode;

    /**
     * 物料名称
     */
    private String materialName;

    /**
     * 计量单位
     */
    private String unit;

    /**
     * 所在季度
     */
    private String quarter;

    /**
     * 开始时间（年月）
     */
    private String startDate;

    /**
     * 结束时间（年月）
     */
    private String endDate;

    /**
     * 预算总量
     */
    private BigDecimal budgetTotalQuantity;

    /**
     * 已采购量
     */
    private BigDecimal purchasedQuantity;

    /**
     * 可采购量
     */
    private BigDecimal availableQuantity;

    /**
     * 本次采购量
     */
    private BigDecimal currentPurchaseQuantity;

    /**
     * 不含税单价（元）
     */
    private BigDecimal unitPriceExcludingTax;

    /**
     * 不含税总价（元）
     */
    private BigDecimal totalPriceExcludingTax;

    /**
     * 含税总价（元）
     */
    private BigDecimal totalPriceIncludingTax;

    /**
     * 税额（元）
     */
    private BigDecimal taxAmount;



    /**
     * 关联采购执行依据主表ID
     */
    private String executionBasisId;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 创建人
     */
    private String createBy;

    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     * 更新人
     */
    private String updateBy;

    /**
     * 租户ID
     */
    private Integer tenantId;

    /**
     * 删除标识
     */
    private Integer delFlag;

    /**
     * 所属部门代码
     */
    private String sysOrgCode;

    /**
     * 明细列表
     */
    private List<ProcurementApplicationDetailEntity> details;

    /**
     * 计算价格信息
     * 根据本次采购量和不含税单价计算各项价格
     */
    public void calculatePrices() {
        if (this.currentPurchaseQuantity != null && this.unitPriceExcludingTax != null) {
            // 计算不含税总价
            this.totalPriceExcludingTax = this.currentPurchaseQuantity.multiply(this.unitPriceExcludingTax);
            
            // 计算含税总价（假设税率13%）
            this.totalPriceIncludingTax = this.totalPriceExcludingTax.multiply(new BigDecimal("1.13"));
            
            // 计算税额
            this.taxAmount = this.totalPriceIncludingTax.subtract(this.totalPriceExcludingTax);
        }
    }

    /**
     * 校验本次采购量是否超过可采购量
     */
    public boolean validatePurchaseQuantity() {
        if (this.currentPurchaseQuantity == null || this.availableQuantity == null) {
            return false;
        }
        return this.currentPurchaseQuantity.compareTo(this.availableQuantity) <= 0;
    }

    /**
     * 校验明细行采购量之和是否等于主表采购量
     */
    public boolean validateDetailQuantitySum() {
        if (this.details == null || this.details.isEmpty() || this.currentPurchaseQuantity == null) {
            return false;
        }
        
        BigDecimal detailSum = this.details.stream()
                .map(ProcurementApplicationDetailEntity::getCurrentPurchaseQuantity)
                .filter(quantity -> quantity != null)
                .reduce(BigDecimal.ZERO, BigDecimal::add);
                
        return this.currentPurchaseQuantity.compareTo(detailSum) == 0;
    }

    /**
     * 加权均分本次采购量到明细行
     */
    public void distributeQuantityToDetails() {
        if (this.details == null || this.details.isEmpty() || 
            this.currentPurchaseQuantity == null || this.budgetTotalQuantity == null ||
            this.budgetTotalQuantity.compareTo(BigDecimal.ZERO) == 0) {
            return;
        }

        BigDecimal remainingQuantity = this.currentPurchaseQuantity;
        
        // 按预算总量比例分配
        for (int i = 0; i < this.details.size(); i++) {
            ProcurementApplicationDetailEntity detail = this.details.get(i);
            if (detail.getBudgetTotalQuantity() != null) {
                if (i == this.details.size() - 1) {
                    // 最后一行分配剩余数量，避免精度问题
                    detail.setCurrentPurchaseQuantity(remainingQuantity);
                } else {
                    // 按比例分配：明细行本次采购量 = (明细行预算总量 / 主表预算总量) × 主表本次采购量
                    BigDecimal ratio = detail.getBudgetTotalQuantity().divide(this.budgetTotalQuantity, 6, BigDecimal.ROUND_HALF_UP);
                    BigDecimal detailQuantity = this.currentPurchaseQuantity.multiply(ratio).setScale(4, BigDecimal.ROUND_HALF_UP);
                    detail.setCurrentPurchaseQuantity(detailQuantity);
                    remainingQuantity = remainingQuantity.subtract(detailQuantity);
                }
                
                // 计算明细行价格
                detail.calculatePrices();
            }
        }
    }


}
