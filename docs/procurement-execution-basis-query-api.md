# 采购执行依据查询接口文档

## 概述

本文档描述了根据物料编码查询采购执行依据信息的API接口。这些接口主要用于采购申请功能，支持根据物料编码查询对应的季度预算数据。

## 接口列表

### 1. 根据物料编码查询采购执行依据

**接口地址：** `GET /cost/costProcurementExecutionBasis/findByMaterialCode`

**功能描述：** 根据物料编码查询采购执行依据信息，一个物料编码可能对应多个不同的季度数据

**请求参数：**

| 参数名 | 类型 | 必填 | 描述 |
|--------|------|------|------|
| materialCode | String | 是 | 物料编码 |

**请求示例：**
```http
GET /cost/costProcurementExecutionBasis/findByMaterialCode?materialCode=MAT001
```

**响应示例：**
```json
{
  "success": true,
  "result": [
    {
      "id": "1234567890",
      "materialCode": "MAT001",
      "materialName": "钢材",
      "unit": "kg",
      "quarter": "2025年第一季度",
      "startDate": "2025-01",
      "endDate": "2025-03",
      "totalDemandQuantity": 1000.0000,
      "unitPriceExcludingTax": 5.500000,
      "totalPriceExcludingTax": 5500.00,
      "totalPriceIncludingTax": 6215.00,
      "purchasedQuantity": 200.0000,
      "spentAmountExcludingTax": 1100.00,
      "spentAmountIncludingTax": 1243.00,
      "remainingAmountExcludingTax": 4400.00,
      "remainingAmountIncludingTax": 4972.00,
      "createTime": "2025-08-26 10:00:00",
      "createBy": "admin",
      "details": [
        {
          "id": "detail-1",
          "budgetCode": "BUD001",
          "budgetName": "原材料预算",
          "demandQuantity": 500.0000,
          "unitPriceExcludingTax": 5.500000,
          "totalPriceExcludingTax": 2750.00,
          "totalPriceIncludingTax": 3107.50
        }
      ]
    },
    {
      "id": "1234567891",
      "materialCode": "MAT001",
      "materialName": "钢材",
      "unit": "kg",
      "quarter": "2025年第二季度",
      "startDate": "2025-04",
      "endDate": "2025-06",
      "totalDemandQuantity": 1200.0000,
      "unitPriceExcludingTax": 5.800000,
      "totalPriceExcludingTax": 6960.00,
      "totalPriceIncludingTax": 7864.80,
      "purchasedQuantity": 0.0000,
      "spentAmountExcludingTax": 0.00,
      "spentAmountIncludingTax": 0.00,
      "remainingAmountExcludingTax": 6960.00,
      "remainingAmountIncludingTax": 7864.80,
      "createTime": "2025-08-26 10:30:00",
      "createBy": "admin",
      "details": [
        {
          "id": "detail-2",
          "budgetCode": "BUD002",
          "budgetName": "设备采购预算",
          "demandQuantity": 600.0000,
          "unitPriceExcludingTax": 5.800000,
          "totalPriceExcludingTax": 3480.00,
          "totalPriceIncludingTax": 3932.40
        }
      ]
    }
  ],
  "timestamp": 1724654400000
}
```

### 2. 根据物料编码和季度查询采购执行依据

**接口地址：** `GET /cost/costProcurementExecutionBasis/findByMaterialCodeAndQuarter`

**功能描述：** 根据物料编码和季度查询特定的采购执行依据信息

**请求参数：**

| 参数名 | 类型 | 必填 | 描述 |
|--------|------|------|------|
| materialCode | String | 是 | 物料编码 |
| quarter | String | 是 | 所在季度（如：2025年第一季度） |

**请求示例：**
```http
GET /cost/costProcurementExecutionBasis/findByMaterialCodeAndQuarter?materialCode=MAT001&quarter=2025年第一季度
```

**响应示例：**
```json
{
  "success": true,
  "result": {
    "id": "1234567890",
    "materialCode": "MAT001",
    "materialName": "钢材",
    "unit": "kg",
    "quarter": "2025年第一季度",
    "startDate": "2025-01",
    "endDate": "2025-03",
    "totalDemandQuantity": 1000.0000,
    "unitPriceExcludingTax": 5.500000,
    "totalPriceExcludingTax": 5500.00,
    "totalPriceIncludingTax": 6215.00,
    "purchasedQuantity": 200.0000,
    "spentAmountExcludingTax": 1100.00,
    "spentAmountIncludingTax": 1243.00,
    "remainingAmountExcludingTax": 4400.00,
    "remainingAmountIncludingTax": 4972.00,
    "createTime": "2025-08-26 10:00:00",
    "createBy": "admin",
    "details": [
      {
        "id": "detail-1",
        "budgetCode": "BUD001",
        "budgetName": "原材料预算",
        "demandQuantity": 500.0000,
        "unitPriceExcludingTax": 5.500000,
        "totalPriceExcludingTax": 2750.00,
        "totalPriceIncludingTax": 3107.50,
        "purchasedQuantity": 100.0000,
        "spentAmountExcludingTax": 550.00,
        "spentAmountIncludingTax": 621.50,
        "remainingAmountExcludingTax": 2200.00,
        "remainingAmountIncludingTax": 2486.00
      }
    ]
  },
  "timestamp": 1724654400000
}
```

## 错误响应

### 参数错误
```json
{
  "success": false,
  "message": "物料编码不能为空",
  "timestamp": 1724654400000
}
```

### 数据不存在
```json
{
  "success": false,
  "message": "未找到对应的采购执行依据数据",
  "timestamp": 1724654400000
}
```

### 系统错误
```json
{
  "success": false,
  "message": "查询失败: 数据库连接异常",
  "timestamp": 1724654400000
}
```

## 业务场景

### 1. 采购申请新增场景
- **从采购申请列表页点击"新增"按钮**：
  1. 用户选择物料编码
  2. 调用 `findByMaterialCode` 接口获取该物料的所有季度数据
  3. 用户选择具体的季度
  4. 系统自动填充采购申请信息

### 2. 从采购执行依据页面跳转场景
- **从采购执行依据列表页点击"采购申请"按钮**：
  1. 系统已知物料编码和季度
  2. 调用 `findByMaterialCodeAndQuarter` 接口获取具体数据
  3. 系统自动填充采购申请信息

## 数据说明

### 主要字段说明
- **totalDemandQuantity**: 预算总量，来源于季度预算
- **purchasedQuantity**: 已采购量，统计已完成的采购订单
- **可采购量**: 预算总量 - 已采购量 - 审批中量（需要在前端计算）
- **unitPriceExcludingTax**: 不含税单价，用于计算采购申请的价格
- **details**: 明细数据，包含各个预算项目的具体信息

### 排序规则
- **findByMaterialCode**: 按季度升序，创建时间降序
- **findByMaterialCodeAndQuarter**: 按创建时间降序，取最新一条

## 注意事项

1. **参数校验**: 所有必填参数都会进行非空校验，包括去除前后空格
2. **数据完整性**: 返回的数据包含完整的明细信息，便于采购申请页面使用
3. **性能考虑**: 查询会自动加载明细数据，对于大量明细的情况需要注意性能
4. **租户隔离**: 查询会自动过滤已删除的数据，并考虑多租户隔离
5. **异常处理**: 所有异常都会被捕获并返回友好的错误信息
