# 采购申请金额更新功能测试文档

## 测试目标

验证采购申请新增成功后，系统能够正确更新采购执行依据的已采购量和相关金额字段，包括已支出金额和剩余金额的计算。

## 测试准备

### 1. 准备采购执行依据测试数据

```sql
-- 插入采购执行依据主表数据
INSERT INTO cost_procurement_execution_basis (
    id, material_code, material_name, unit, quarter, start_date, end_date,
    total_demand_quantity, purchased_quantity, 
    unit_price_excluding_tax, total_price_excluding_tax, total_price_including_tax,
    spent_amount_excluding_tax, spent_amount_including_tax,
    remaining_amount_excluding_tax, remaining_amount_including_tax,
    create_time, create_by, tenant_id, del_flag
) VALUES (
    'exec-basis-amount-test-001', 'MAT002', '铝材', 'kg', '2025年第一季度', '2025-01', '2025-03',
    2000.0000, 0.0000,
    8.500000, 17000.00, 19210.00,
    0.00, 0.00,
    17000.00, 19210.00,
    NOW(), 'test-user', 1, 0
);

-- 插入采购执行依据明细数据
INSERT INTO cost_procurement_execution_basis_detail (
    id, execution_basis_id, budget_code, budget_name,
    demand_quantity, purchased_quantity, 
    unit_price_excluding_tax, total_price_excluding_tax, total_price_including_tax,
    spent_amount_excluding_tax, spent_amount_including_tax,
    remaining_amount_excluding_tax, remaining_amount_including_tax,
    quarterly_budget_id, create_time, create_by, tenant_id, del_flag
) VALUES 
(
    'exec-detail-amount-test-001', 'exec-basis-amount-test-001', 'BUD003', '铝材预算A',
    1200.0000, 0.0000,
    8.500000, 10200.00, 11526.00,
    0.00, 0.00,
    10200.00, 11526.00,
    'budget-id-201', NOW(), 'test-user', 1, 0
),
(
    'exec-detail-amount-test-002', 'exec-basis-amount-test-001', 'BUD004', '铝材预算B',
    800.0000, 0.0000,
    8.500000, 6800.00, 7684.00,
    0.00, 0.00,
    6800.00, 7684.00,
    'budget-id-202', NOW(), 'test-user', 1, 0
);
```

### 2. 验证初始状态

```sql
-- 查询主表初始状态
SELECT id, material_code, purchased_quantity, 
       spent_amount_excluding_tax, spent_amount_including_tax,
       remaining_amount_excluding_tax, remaining_amount_including_tax
FROM cost_procurement_execution_basis 
WHERE id = 'exec-basis-amount-test-001';

-- 查询明细表初始状态
SELECT id, budget_code, purchased_quantity,
       spent_amount_excluding_tax, spent_amount_including_tax,
       remaining_amount_excluding_tax, remaining_amount_including_tax
FROM cost_procurement_execution_basis_detail 
WHERE execution_basis_id = 'exec-basis-amount-test-001';
```

**预期初始状态：**
- 主表：purchased_quantity = 0, spent_amount_excluding_tax = 0, spent_amount_including_tax = 0
- 明细表：所有金额字段都为0

## 测试执行

### 1. 调用采购申请新增接口

**请求地址：** `POST /cost/procurementApplication`

**请求数据：**
```json
{
  "materialCode": "MAT002",
  "materialName": "铝材",
  "unit": "kg",
  "quarter": "2025年第一季度",
  "startDate": "2025-01",
  "endDate": "2025-03",
  "budgetTotalQuantity": 2000.0000,
  "purchasedQuantity": 0.0000,
  "availableQuantity": 2000.0000,
  "currentPurchaseQuantity": 600.0000,
  "unitPriceExcludingTax": 8.500000,
  "executionBasisId": "exec-basis-amount-test-001",
  "details": [
    {
      "budgetCode": "BUD003",
      "budgetName": "铝材预算A",
      "budgetTotalQuantity": 1200.0000,
      "availableQuantity": 1200.0000,
      "currentPurchaseQuantity": 360.0000,
      "unitPriceExcludingTax": 8.500000,
      "executionBasisDetailId": "exec-detail-amount-test-001",
      "quarterlyBudgetId": "budget-id-201"
    },
    {
      "budgetCode": "BUD004",
      "budgetName": "铝材预算B",
      "budgetTotalQuantity": 800.0000,
      "availableQuantity": 800.0000,
      "currentPurchaseQuantity": 240.0000,
      "unitPriceExcludingTax": 8.500000,
      "executionBasisDetailId": "exec-detail-amount-test-002",
      "quarterlyBudgetId": "budget-id-202"
    }
  ]
}
```

### 2. 验证响应结果

**预期响应：**
```json
{
  "success": true,
  "result": {
    "id": "生成的采购申请ID",
    "applicationNo": "PA202508260002",
    "materialCode": "MAT002",
    "materialName": "铝材",
    "currentPurchaseQuantity": 600.0000,
    "unitPriceExcludingTax": 8.500000,
    "totalPriceExcludingTax": 5100.00,
    "totalPriceIncludingTax": 5763.00,
    "taxAmount": 663.00,
    "details": [
      {
        "budgetCode": "BUD003",
        "currentPurchaseQuantity": 360.0000,
        "totalPriceExcludingTax": 3060.00,
        "totalPriceIncludingTax": 3457.80,
        "taxAmount": 397.80
      },
      {
        "budgetCode": "BUD004",
        "currentPurchaseQuantity": 240.0000,
        "totalPriceExcludingTax": 2040.00,
        "totalPriceIncludingTax": 2305.20,
        "taxAmount": 265.20
      }
    ]
  }
}
```

## 测试验证

### 1. 验证采购执行依据主表更新

```sql
SELECT id, material_code, purchased_quantity, 
       spent_amount_excluding_tax, spent_amount_including_tax,
       remaining_amount_excluding_tax, remaining_amount_including_tax
FROM cost_procurement_execution_basis 
WHERE id = 'exec-basis-amount-test-001';
```

**预期结果：**
- `purchased_quantity`: 0.0000 → 600.0000
- `spent_amount_excluding_tax`: 0.00 → 5100.00
- `spent_amount_including_tax`: 0.00 → 5763.00
- `remaining_amount_excluding_tax`: 17000.00 → 11900.00 (17000 - 5100)
- `remaining_amount_including_tax`: 19210.00 → 13447.00 (19210 - 5763)

### 2. 验证采购执行依据明细表更新

```sql
SELECT id, budget_code, purchased_quantity,
       spent_amount_excluding_tax, spent_amount_including_tax,
       remaining_amount_excluding_tax, remaining_amount_including_tax
FROM cost_procurement_execution_basis_detail 
WHERE execution_basis_id = 'exec-basis-amount-test-001'
ORDER BY budget_code;
```

**预期结果：**

**BUD003 (铝材预算A)：**
- `purchased_quantity`: 0.0000 → 360.0000
- `spent_amount_excluding_tax`: 0.00 → 3060.00
- `spent_amount_including_tax`: 0.00 → 3457.80
- `remaining_amount_excluding_tax`: 10200.00 → 7140.00 (10200 - 3060)
- `remaining_amount_including_tax`: 11526.00 → 8068.20 (11526 - 3457.80)

**BUD004 (铝材预算B)：**
- `purchased_quantity`: 0.0000 → 240.0000
- `spent_amount_excluding_tax`: 0.00 → 2040.00
- `spent_amount_including_tax`: 0.00 → 2305.20
- `remaining_amount_excluding_tax`: 6800.00 → 4760.00 (6800 - 2040)
- `remaining_amount_including_tax`: 7684.00 → 5378.80 (7684 - 2305.20)

### 3. 验证数据一致性

```sql
-- 验证主表和明细表数据一致性
SELECT 
    main.purchased_quantity as main_purchased,
    SUM(detail.purchased_quantity) as detail_purchased_sum,
    main.spent_amount_excluding_tax as main_spent_excluding,
    SUM(detail.spent_amount_excluding_tax) as detail_spent_excluding_sum,
    main.spent_amount_including_tax as main_spent_including,
    SUM(detail.spent_amount_including_tax) as detail_spent_including_sum
FROM cost_procurement_execution_basis main
LEFT JOIN cost_procurement_execution_basis_detail detail ON main.id = detail.execution_basis_id
WHERE main.id = 'exec-basis-amount-test-001'
GROUP BY main.id;
```

**预期结果：**
- main_purchased = detail_purchased_sum = 600.0000
- main_spent_excluding = detail_spent_excluding_sum = 5100.00
- main_spent_including = detail_spent_including_sum = 5763.00

## 多次采购测试

### 1. 再次创建采购申请

使用相同的物料和季度，创建第二个采购申请：

**请求数据：**
```json
{
  "materialCode": "MAT002",
  "materialName": "铝材",
  "quarter": "2025年第一季度",
  "currentPurchaseQuantity": 400.0000,
  "unitPriceExcludingTax": 8.500000,
  "details": [
    {
      "budgetCode": "BUD003",
      "currentPurchaseQuantity": 240.0000,
      "unitPriceExcludingTax": 8.500000
    },
    {
      "budgetCode": "BUD004",
      "currentPurchaseQuantity": 160.0000,
      "unitPriceExcludingTax": 8.500000
    }
  ]
}
```

### 2. 验证累计更新

**预期主表结果：**
- `purchased_quantity`: 600.0000 → 1000.0000 (600 + 400)
- `spent_amount_excluding_tax`: 5100.00 → 8500.00 (5100 + 3400)
- `spent_amount_including_tax`: 5763.00 → 9605.00 (5763 + 3842)

## 异常测试场景

### 1. 剩余金额不足

创建一个超出预算总额的采购申请，验证系统的处理：

```json
{
  "currentPurchaseQuantity": 3000.0000,
  "unitPriceExcludingTax": 8.500000
}
```

**预期结果：** 系统应该记录警告日志，但不阻止操作

### 2. 采购执行依据不存在

使用不存在的物料编码或季度：

```json
{
  "materialCode": "MAT999",
  "quarter": "2025年第一季度"
}
```

**预期结果：** 记录警告日志，跳过采购执行依据更新

## 清理测试数据

```sql
-- 删除测试数据
DELETE FROM cost_procurement_application_detail WHERE application_id IN (
    SELECT id FROM cost_procurement_application WHERE material_code = 'MAT002'
);
DELETE FROM cost_procurement_application WHERE material_code = 'MAT002';
DELETE FROM cost_procurement_execution_basis_detail WHERE execution_basis_id = 'exec-basis-amount-test-001';
DELETE FROM cost_procurement_execution_basis WHERE id = 'exec-basis-amount-test-001';
```

## 总结

通过以上测试用例，可以全面验证采购申请新增功能对采购执行依据金额字段的更新逻辑，确保：

1. **数量更新正确**：已采购量正确累加
2. **金额更新正确**：已支出金额正确累加
3. **剩余金额计算正确**：剩余金额 = 总金额 - 已支出金额
4. **主表明细一致**：主表和明细表数据保持一致
5. **多次采购累计**：支持多次采购的累计更新
6. **异常处理完善**：异常情况下的处理逻辑正确
