# 采购申请CRUD接口文档

## 概述

本文档描述了采购申请的完整CRUD操作接口，包括新增、编辑、查询、删除等功能。采购申请不涉及审批流程，支持主子表的级联操作。

## 接口列表

### 1. 分页查询采购申请列表

**接口地址：** `GET /cost/procurementApplication/list`

**功能描述：** 分页查询采购申请列表，支持多种查询条件，按创建时间倒序排列

**请求参数：**

| 参数名 | 类型 | 必填 | 描述 |
|--------|------|------|------|
| applicationNo | String | 否 | 采购申请单号（支持模糊查询） |
| materialCode | String | 否 | 物料编码（支持模糊查询） |
| materialName | String | 否 | 物料名称（支持模糊查询） |
| quarter | String | 否 | 所在季度（精确查询） |
| createBy | String | 否 | 创建人（精确查询） |
| createTime | String | 否 | 创建时间（格式：yyyy-MM-dd） |
| pageNo | Integer | 否 | 页码，默认1 |
| pageSize | Integer | 否 | 每页数量，默认10 |

**请求示例：**
```http
GET /cost/procurementApplication/list?materialName=钢材&quarter=2025年第一季度&pageNo=1&pageSize=10
```

**响应示例：**
```json
{
  "success": true,
  "result": {
    "current": 1,
    "size": 10,
    "total": 25,
    "records": [
      {
        "id": "app-id-123",
        "applicationNo": "PA202508260001",
        "materialCode": "MAT001",
        "materialName": "钢材",
        "unit": "kg",
        "quarter": "2025年第一季度",
        "startDate": "2025-01",
        "endDate": "2025-03",
        "budgetTotalQuantity": 1000.0000,
        "purchasedQuantity": 200.0000,
        "availableQuantity": 800.0000,
        "currentPurchaseQuantity": 500.0000,
        "unitPriceExcludingTax": 10.500000,
        "totalPriceExcludingTax": 5250.00,
        "totalPriceIncludingTax": 5932.50,
        "taxAmount": 682.50,
        "executionBasisId": "exec-basis-id-123",
        "createTime": "2025-08-26 10:00:00",
        "createBy": "admin"
      }
    ]
  },
  "timestamp": 1724654400000
}
```

### 2. 生成采购申请单号

**接口地址：** `GET /cost/procurementApplication/generateApplicationNo`

**功能描述：** 自动生成采购申请单号，格式：PA + YYYYMMDD + 4位序号

**请求参数：** 无

**响应示例：**
```json
{
  "success": true,
  "result": "PA202508260001",
  "timestamp": 1724654400000
}
```

### 2. 新增采购申请

**接口地址：** `POST /cost/procurementApplication`

**功能描述：** 创建新的采购申请，包含主表信息和明细表信息。保存成功后会自动更新相关采购执行依据的已采购量和金额信息

**自动更新逻辑：**
1. **更新已采购量**：采购执行依据.已采购量 += 采购申请.本次采购量
2. **更新已支出金额**：
   - 已支出不含税金额 += 采购申请.不含税总价
   - 已支出含税金额 += 采购申请.含税总价
3. **重新计算剩余金额**：
   - 剩余不含税金额 = 总不含税金额 - 已支出不含税金额
   - 剩余含税金额 = 总含税金额 - 已支出含税金额
4. **同步更新明细表**：按预算编码匹配，更新对应明细行的数量和金额

**请求参数：**
```json
{
  "materialCode": "MAT001",
  "materialName": "钢材",
  "unit": "kg",
  "quarter": "2025年第一季度",
  "startDate": "2025-01",
  "endDate": "2025-03",
  "budgetTotalQuantity": 1000.0000,
  "purchasedQuantity": 200.0000,
  "availableQuantity": 800.0000,
  "currentPurchaseQuantity": 500.0000,
  "unitPriceExcludingTax": 10.500000,
  "executionBasisId": "exec-basis-id-123",
  "details": [
    {
      "budgetCode": "BUD001",
      "budgetName": "原材料预算",
      "budgetTotalQuantity": 500.0000,
      "availableQuantity": 400.0000,
      "currentPurchaseQuantity": 250.0000,
      "unitPriceExcludingTax": 10.500000,
      "executionBasisDetailId": "detail-id-123",
      "quarterlyBudgetId": "budget-id-123"
    },
    {
      "budgetCode": "BUD002",
      "budgetName": "设备采购预算",
      "budgetTotalQuantity": 500.0000,
      "availableQuantity": 400.0000,
      "currentPurchaseQuantity": 250.0000,
      "unitPriceExcludingTax": 10.500000,
      "executionBasisDetailId": "detail-id-124",
      "quarterlyBudgetId": "budget-id-124"
    }
  ]
}
```

**响应示例：**
```json
{
  "success": true,
  "result": {
    "id": "app-id-123",
    "applicationNo": "PA202508260001",
    "materialCode": "MAT001",
    "materialName": "钢材",
    "unit": "kg",
    "quarter": "2025年第一季度",
    "startDate": "2025-01",
    "endDate": "2025-03",
    "budgetTotalQuantity": 1000.0000,
    "purchasedQuantity": 200.0000,
    "availableQuantity": 800.0000,
    "currentPurchaseQuantity": 500.0000,
    "unitPriceExcludingTax": 10.500000,
    "totalPriceExcludingTax": 5250.00,
    "totalPriceIncludingTax": 5932.50,
    "taxAmount": 682.50,
    "executionBasisId": "exec-basis-id-123",
    "createTime": "2025-08-26 10:00:00",
    "createBy": "admin",
    "details": [
      {
        "id": "detail-app-id-123",
        "applicationId": "app-id-123",
        "budgetCode": "BUD001",
        "budgetName": "原材料预算",
        "budgetTotalQuantity": 500.0000,
        "availableQuantity": 400.0000,
        "currentPurchaseQuantity": 250.0000,
        "unitPriceExcludingTax": 10.500000,
        "totalPriceExcludingTax": 2625.00,
        "totalPriceIncludingTax": 2966.25,
        "taxAmount": 341.25,
        "executionBasisDetailId": "detail-id-123",
        "quarterlyBudgetId": "budget-id-123"
      }
    ]
  },
  "timestamp": 1724654400000
}
```

### 3. 编辑采购申请

**接口地址：** `PUT /cost/procurementApplication/{id}`

**功能描述：** 修改现有的采购申请信息，会根据采购量变化差量更新采购执行依据数据

**业务逻辑：**
1. 获取原采购申请数据
2. 计算采购量差值：Δ = 新采购量 - 原采购量
3. 差量更新采购执行依据：
   - 已采购量 = 原已采购量 + Δ
   - 已支出金额 = 原已支出金额 + (Δ × 单价)
4. 确保更新后的数据不为负数

**请求参数：** 与新增接口相同的JSON结构

**响应示例：** 与新增接口相同的响应结构

**特殊说明：**
- 编辑操作会自动计算采购量变化，避免重复累计
- 如果差量更新采购执行依据失败，整个事务会回滚
- 支持增加或减少采购量，系统会自动处理正负差值

### 4. 数据回填接口

**接口地址：** `GET /cost/procurementApplication/{id}`

**功能描述：** 根据采购申请ID查询完整的申请信息，用于编辑页面数据回填

**请求参数：**
- `id`: 采购申请ID（路径参数）

**响应示例：** 与新增接口相同的响应结构

### 5. 删除采购申请

**接口地址：** `DELETE /cost/procurementApplication/{id}`

**功能描述：** 根据ID删除采购申请（逻辑删除）

**请求参数：**
- `id`: 采购申请ID（路径参数）

**响应示例：**
```json
{
  "success": true,
  "timestamp": 1724654400000
}
```

### 6. 批量删除采购申请

**接口地址：** `DELETE /cost/procurementApplication/batch`

**功能描述：** 批量删除采购申请

**请求参数：**
```json
["app-id-123", "app-id-124", "app-id-125"]
```

**响应示例：**
```json
{
  "success": true,
  "timestamp": 1724654400000
}
```

### 7. 加权均分接口

**接口地址：** `POST /cost/procurementApplication/distributeQuantity`

**功能描述：** 将主表的本次采购量按预算总量比例分配到明细行

**请求参数：** 采购申请完整DTO

**响应示例：** 返回分配后的采购申请信息

**分配逻辑：**
- 明细行本次采购量 = (明细行预算总量 / 主表预算总量) × 主表本次采购量
- 最后一行分配剩余数量，避免精度问题
- 自动计算每个明细行的价格信息

### 8. 根据申请单号查询

**接口地址：** `GET /cost/procurementApplication/findByApplicationNo`

**功能描述：** 根据申请单号查询采购申请详情

**请求参数：**
- `applicationNo`: 申请单号（查询参数）

**响应示例：** 与详情查询接口相同的响应结构

## 业务规则

### 1. 采购执行依据更新规则

**更新时机：** 采购申请保存成功后立即执行

**更新范围：** 根据采购申请中的物料编码（materialCode）和季度（quarter）查找对应的采购执行依据记录

**更新逻辑：**
- **主表数量更新**：`purchased_quantity += 采购申请.current_purchase_quantity`
- **主表金额更新**：
  - `spent_amount_excluding_tax += 采购申请.total_price_excluding_tax`
  - `spent_amount_including_tax += 采购申请.total_price_including_tax`
  - `remaining_amount_excluding_tax = total_price_excluding_tax - spent_amount_excluding_tax`
  - `remaining_amount_including_tax = total_price_including_tax - spent_amount_including_tax`
- **明细表更新**：通过budget_code匹配，按相同逻辑更新对应明细行的数量和金额

**匹配规则：**
- 主表匹配：`material_code = 采购申请.material_code AND quarter = 采购申请.quarter`
- 明细表匹配：`execution_basis_id = 主表ID AND budget_code = 明细行.budget_code`

**事务处理：** 采购申请保存和采购执行依据更新在同一个事务中，确保数据一致性

**异常处理：** 如果更新采购执行依据失败，整个事务会回滚，采购申请保存也会失败

**数据校验：** 系统会检查更新后的剩余金额，如果为负数会记录警告日志但不阻止操作

### 2. 数据校验规则

- **基本信息校验**：物料编码、物料名称、本次采购量等必填字段不能为空
- **数量校验**：本次采购量必须大于0，且不能超过可采购量
- **明细校验**：明细列表不能为空，明细行采购量之和必须等于主表采购量
- **明细行校验**：每个明细行的本次采购量不能超过其可采购量

### 2. 价格计算规则

- **不含税总价** = 本次采购量 × 不含税单价
- **含税总价** = 不含税总价 × 1.13（假设税率13%）
- **税额** = 含税总价 - 不含税总价

### 3. 申请单号生成规则

- **格式**：PA + YYYYMMDD + 4位序号
- **示例**：PA202508260001
- **逻辑**：查询当天已有的最大序号，自动递增

### 4. 数据关联规则

- **执行依据关联**：通过executionBasisId关联采购执行依据主表
- **明细关联**：通过executionBasisDetailId关联采购执行依据明细表
- **预算关联**：通过quarterlyBudgetId关联季度预算表

## 错误处理

### 参数校验错误
```json
{
  "success": false,
  "message": "物料编码不能为空",
  "timestamp": 1724654400000
}
```

### 业务逻辑错误
```json
{
  "success": false,
  "message": "本次采购量不能超过可采购量",
  "timestamp": 1724654400000
}
```

### 数据不存在错误
```json
{
  "success": false,
  "message": "采购申请不存在",
  "timestamp": 1724654400000
}
```

### 系统错误
```json
{
  "success": false,
  "message": "新增失败: 数据库连接异常",
  "timestamp": 1724654400000
}
```

### 采购执行依据更新失败
```json
{
  "success": false,
  "message": "保存采购申请成功，但更新采购执行依据失败: 未找到对应的采购执行依据",
  "timestamp": 1724654400000
}
```

## 事务管理

- **新增操作**：主表和明细表在同一事务中保存，确保数据一致性
- **更新操作**：先删除原有明细，再插入新明细，保证数据完整性
- **删除操作**：级联删除主表和明细表数据（逻辑删除）
- **异常回滚**：任何操作失败都会回滚整个事务

## 使用场景

1. **从采购执行依据跳转**：自动填充执行依据的相关数据
2. **从采购申请列表新增**：支持手动选择物料和季度
3. **编辑现有申请**：支持修改申请信息和明细数据
4. **加权均分**：快速分配采购量到各个预算项目
5. **数据查询**：支持按ID和申请单号查询详情
