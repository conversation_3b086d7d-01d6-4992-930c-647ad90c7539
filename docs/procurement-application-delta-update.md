# 采购申请编辑差量更新逻辑文档

## 概述

当用户编辑采购申请时，系统需要根据采购量的变化来调整采购执行依据中的已采购量和已支出金额，避免重复累计或丢失历史数据。本文档详细说明了差量更新的实现逻辑。

## 业务场景

### 场景1：增加采购量
- **原采购量**：300kg
- **新采购量**：500kg
- **差量**：+200kg
- **结果**：采购执行依据的已采购量增加200kg，已支出金额相应增加

### 场景2：减少采购量
- **原采购量**：500kg
- **新采购量**：300kg
- **差量**：-200kg
- **结果**：采购执行依据的已采购量减少200kg，已支出金额相应减少

### 场景3：采购量不变
- **原采购量**：300kg
- **新采购量**：300kg
- **差量**：0kg
- **结果**：跳过更新，不影响采购执行依据数据

## 实现逻辑

### 1. 差量计算

```java
// 主表差量计算
BigDecimal originalQuantity = originalEntity.getCurrentPurchaseQuantity();
BigDecimal newQuantity = newEntity.getCurrentPurchaseQuantity();
BigDecimal quantityDelta = newQuantity.subtract(originalQuantity);

// 明细表差量计算（按预算编码匹配）
for (ProcurementApplicationDetailEntity newDetail : newDetails) {
    ProcurementApplicationDetailEntity originalDetail = originalDetailMap.get(newDetail.getBudgetCode());
    BigDecimal originalDetailQuantity = originalDetail != null ? 
            originalDetail.getCurrentPurchaseQuantity() : BigDecimal.ZERO;
    BigDecimal newDetailQuantity = newDetail.getCurrentPurchaseQuantity();
    BigDecimal detailQuantityDelta = newDetailQuantity.subtract(originalDetailQuantity);
}
```

### 2. 主表更新逻辑

```java
// 更新已采购量
BigDecimal newPurchasedQuantity = originalPurchasedQuantity.add(quantityDelta);

// 计算金额差值
BigDecimal amountDeltaExcludingTax = quantityDelta.multiply(unitPriceExcludingTax);
BigDecimal amountDeltaIncludingTax = amountDeltaExcludingTax.multiply(new BigDecimal("1.13"));

// 更新已支出金额
BigDecimal newSpentAmountExcludingTax = originalSpentAmountExcludingTax.add(amountDeltaExcludingTax);
BigDecimal newSpentAmountIncludingTax = originalSpentAmountIncludingTax.add(amountDeltaIncludingTax);

// 计算剩余金额
BigDecimal newRemainingAmountExcludingTax = totalAmountExcludingTax.subtract(newSpentAmountExcludingTax);
BigDecimal newRemainingAmountIncludingTax = totalAmountIncludingTax.subtract(newSpentAmountIncludingTax);
```

### 3. 明细表更新逻辑

明细表的更新逻辑与主表类似，但需要按预算编码（budget_code）进行匹配：

```java
// 通过预算编码匹配对应的采购执行依据明细
LambdaQueryWrapper<CostProcurementExecutionBasisDetail> detailWrapper = new LambdaQueryWrapper<>();
detailWrapper.eq(CostProcurementExecutionBasisDetail::getExecutionBasisId, executionBasisId)
        .eq(CostProcurementExecutionBasisDetail::getBudgetCode, budgetCode);

// 应用相同的差量更新逻辑
```

## 数据校验

### 1. 负数校验

```java
// 确保已采购量不为负数
if (newPurchasedQuantity.compareTo(BigDecimal.ZERO) < 0) {
    throw new IllegalArgumentException("更新后的已采购量不能为负数");
}

// 确保已支出金额不为负数
if (newSpentAmountExcludingTax.compareTo(BigDecimal.ZERO) < 0) {
    throw new IllegalArgumentException("更新后的已支出金额不能为负数");
}
```

### 2. 数据一致性校验

- 主表采购量差值 = 所有明细行采购量差值之和
- 主表金额差值 = 所有明细行金额差值之和

## 事务处理

```java
@Transactional(rollbackFor = Exception.class)
public ProcurementApplicationEntity update(ProcurementApplicationEntity entity) {
    // 1. 获取原数据
    ProcurementApplicationEntity existingEntity = procurementApplicationRepository.getById(entity.getId());
    
    // 2. 更新采购申请
    ProcurementApplicationEntity updatedEntity = procurementApplicationRepository.update(entity);
    
    // 3. 差量更新采购执行依据
    try {
        procurementApplicationRepository.updateExecutionBasisPurchasedQuantityByDelta(existingEntity, updatedEntity);
    } catch (Exception e) {
        // 抛出异常，触发事务回滚
        throw new RuntimeException("差量更新采购执行依据失败: " + e.getMessage(), e);
    }
    
    return updatedEntity;
}
```

## 异常处理

### 1. 业务异常

- **采购执行依据不存在**：记录警告日志，跳过更新
- **明细预算编码不匹配**：记录警告日志，跳过该明细
- **数据校验失败**：抛出IllegalArgumentException，回滚事务

### 2. 系统异常

- **数据库连接异常**：抛出RuntimeException，回滚事务
- **并发更新冲突**：抛出RuntimeException，回滚事务

## 日志记录

### 1. 信息日志

```java
log.info("开始差量更新采购执行依据，物料编码: {}, 季度: {}, 原采购量: {}, 新采购量: {}, 差量: {}",
        materialCode, quarter, originalQuantity, newQuantity, quantityDelta);

log.info("差量更新采购执行依据主表成功，ID: {}, 已采购量: {} -> {}, 已支出金额: {} -> {}",
        executionBasisId, originalPurchasedQuantity, newPurchasedQuantity, 
        originalSpentAmount, newSpentAmount);
```

### 2. 警告日志

```java
log.warn("未找到对应的采购执行依据，物料编码: {}, 季度: {}", materialCode, quarter);
log.warn("明细预算编码为空，跳过该明细");
```

### 3. 错误日志

```java
log.error("差量更新采购执行依据失败，物料编码: {}, 季度: {}", materialCode, quarter, e);
```

## 性能优化

### 1. 批量更新

对于多个明细行的更新，考虑使用批量更新减少数据库交互次数：

```java
// 收集所有需要更新的明细
List<CostProcurementExecutionBasisDetail> updateList = new ArrayList<>();
// ... 处理逻辑
// 批量更新
costProcurementExecutionBasisDetailService.updateBatchById(updateList);
```

### 2. 缓存优化

对于频繁查询的采购执行依据数据，可以考虑添加缓存：

```java
@Cacheable(value = "executionBasis", key = "#materialCode + '_' + #quarter")
public CostProcurementExecutionBasis getExecutionBasis(String materialCode, String quarter) {
    // 查询逻辑
}
```

## 测试用例

### 1. 正常流程测试

- 测试增加采购量的差量更新
- 测试减少采购量的差量更新
- 测试采购量不变的情况

### 2. 异常情况测试

- 测试采购执行依据不存在的情况
- 测试明细预算编码不匹配的情况
- 测试数据校验失败的情况

### 3. 边界条件测试

- 测试采购量为0的情况
- 测试单价为0的情况
- 测试极大数值的计算精度

## 总结

差量更新机制确保了采购申请编辑时采购执行依据数据的准确性和一致性。通过计算采购量差值并应用到相关字段，避免了重复累计的问题，同时保持了历史数据的完整性。完善的异常处理和事务管理保证了数据的可靠性。
