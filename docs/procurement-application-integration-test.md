# 采购申请集成测试文档

## 测试场景

本文档描述了采购申请新增功能的集成测试场景，重点验证采购申请保存成功后自动更新采购执行依据已采购量的功能。

## 测试准备

### 1. 准备采购执行依据数据

首先需要在数据库中准备采购执行依据的测试数据：

```sql
-- 插入采购执行依据主表数据
INSERT INTO cost_procurement_execution_basis (
    id, material_code, material_name, unit, quarter, start_date, end_date,
    total_demand_quantity, purchased_quantity, unit_price_excluding_tax,
    total_price_excluding_tax, total_price_including_tax,
    create_time, create_by, tenant_id, del_flag
) VALUES (
    'exec-basis-test-001', 'MAT001', '钢材', 'kg', '2025年第一季度', '2025-01', '2025-03',
    1000.0000, 0.0000, 10.500000,
    10500.00, 11865.00,
    NOW(), 'test-user', 1, 0
);

-- 插入采购执行依据明细数据
INSERT INTO cost_procurement_execution_basis_detail (
    id, execution_basis_id, budget_code, budget_name,
    demand_quantity, purchased_quantity, unit_price_excluding_tax,
    total_price_excluding_tax, total_price_including_tax,
    quarterly_budget_id, create_time, create_by, tenant_id, del_flag
) VALUES 
(
    'exec-detail-test-001', 'exec-basis-test-001', 'BUD001', '原材料预算',
    500.0000, 0.0000, 10.500000,
    5250.00, 5932.50,
    'budget-id-123', NOW(), 'test-user', 1, 0
),
(
    'exec-detail-test-002', 'exec-basis-test-001', 'BUD002', '设备采购预算',
    500.0000, 0.0000, 10.500000,
    5250.00, 5932.50,
    'budget-id-124', NOW(), 'test-user', 1, 0
);
```

### 2. 验证初始状态

查询采购执行依据的初始已采购量：

```sql
-- 查询主表已采购量
SELECT id, material_code, purchased_quantity 
FROM cost_procurement_execution_basis 
WHERE id = 'exec-basis-test-001';

-- 查询明细表已采购量
SELECT id, budget_code, purchased_quantity 
FROM cost_procurement_execution_basis_detail 
WHERE execution_basis_id = 'exec-basis-test-001';
```

预期结果：所有的 `purchased_quantity` 都应该是 0.0000

## 测试执行

### 1. 调用采购申请新增接口

**请求地址：** `POST /cost/procurementApplication`

**请求数据：**
```json
{
  "materialCode": "MAT001",
  "materialName": "钢材",
  "unit": "kg",
  "quarter": "2025年第一季度",
  "startDate": "2025-01",
  "endDate": "2025-03",
  "budgetTotalQuantity": 1000.0000,
  "purchasedQuantity": 0.0000,
  "availableQuantity": 1000.0000,
  "currentPurchaseQuantity": 300.0000,
  "unitPriceExcludingTax": 10.500000,
  "executionBasisId": "exec-basis-test-001",
  "details": [
    {
      "budgetCode": "BUD001",
      "budgetName": "原材料预算",
      "budgetTotalQuantity": 500.0000,
      "availableQuantity": 500.0000,
      "currentPurchaseQuantity": 150.0000,
      "unitPriceExcludingTax": 10.500000,
      "executionBasisDetailId": "exec-detail-test-001",
      "quarterlyBudgetId": "budget-id-123"
    },
    {
      "budgetCode": "BUD002",
      "budgetName": "设备采购预算",
      "budgetTotalQuantity": 500.0000,
      "availableQuantity": 500.0000,
      "currentPurchaseQuantity": 150.0000,
      "unitPriceExcludingTax": 10.500000,
      "executionBasisDetailId": "exec-detail-test-002",
      "quarterlyBudgetId": "budget-id-124"
    }
  ]
}
```

### 2. 验证响应结果

**预期响应：**
```json
{
  "success": true,
  "result": {
    "id": "生成的采购申请ID",
    "applicationNo": "PA202508260001",
    "materialCode": "MAT001",
    "materialName": "钢材",
    "currentPurchaseQuantity": 300.0000,
    "totalPriceExcludingTax": 3150.00,
    "totalPriceIncludingTax": 3559.50,
    "taxAmount": 409.50,
    "details": [
      {
        "budgetCode": "BUD001",
        "currentPurchaseQuantity": 150.0000,
        "totalPriceExcludingTax": 1575.00,
        "totalPriceIncludingTax": 1779.75,
        "taxAmount": 204.75
      },
      {
        "budgetCode": "BUD002",
        "currentPurchaseQuantity": 150.0000,
        "totalPriceExcludingTax": 1575.00,
        "totalPriceIncludingTax": 1779.75,
        "taxAmount": 204.75
      }
    ]
  }
}
```

## 测试验证

### 1. 验证采购执行依据主表更新

```sql
SELECT id, material_code, purchased_quantity 
FROM cost_procurement_execution_basis 
WHERE id = 'exec-basis-test-001';
```

**预期结果：**
- `purchased_quantity` 应该从 0.0000 更新为 300.0000

### 2. 验证采购执行依据明细表更新

```sql
SELECT id, budget_code, purchased_quantity 
FROM cost_procurement_execution_basis_detail 
WHERE execution_basis_id = 'exec-basis-test-001'
ORDER BY budget_code;
```

**预期结果：**
- BUD001 的 `purchased_quantity` 应该从 0.0000 更新为 150.0000
- BUD002 的 `purchased_quantity` 应该从 0.0000 更新为 150.0000

### 3. 验证采购申请数据保存

```sql
SELECT id, application_no, material_code, current_purchase_quantity 
FROM cost_procurement_application 
WHERE material_code = 'MAT001' 
ORDER BY create_time DESC 
LIMIT 1;
```

**预期结果：**
- 应该有一条新的采购申请记录
- `current_purchase_quantity` 为 300.0000

## 异常测试场景

### 1. 采购执行依据不存在

**测试数据：** 使用不存在的物料编码或季度

**预期结果：** 接口返回失败，提示"未找到对应的采购执行依据"

### 2. 明细预算编码不匹配

**测试数据：** 使用采购执行依据中不存在的预算编码

**预期结果：** 接口返回失败，提示"未找到对应的采购执行依据明细"

### 3. 数据库事务回滚

**测试方法：** 在更新采购执行依据时模拟数据库异常

**预期结果：** 整个事务回滚，采购申请也不会保存成功

## 性能测试

### 1. 并发测试

**测试场景：** 同时创建多个采购申请，更新同一个采购执行依据

**验证点：** 
- 数据一致性
- 已采购量累加正确性
- 无死锁或并发异常

### 2. 大数据量测试

**测试场景：** 创建包含大量明细行的采购申请

**验证点：**
- 接口响应时间
- 数据库更新性能
- 内存使用情况

## 清理测试数据

测试完成后，清理测试数据：

```sql
-- 删除采购申请数据
DELETE FROM cost_procurement_application_detail WHERE application_id IN (
    SELECT id FROM cost_procurement_application WHERE material_code = 'MAT001'
);
DELETE FROM cost_procurement_application WHERE material_code = 'MAT001';

-- 删除采购执行依据数据
DELETE FROM cost_procurement_execution_basis_detail WHERE execution_basis_id = 'exec-basis-test-001';
DELETE FROM cost_procurement_execution_basis WHERE id = 'exec-basis-test-001';
```

## 总结

通过以上测试场景，可以全面验证采购申请新增功能的正确性，特别是自动更新采购执行依据已采购量的业务逻辑。测试应该覆盖正常流程、异常情况和边界条件，确保功能的稳定性和数据的一致性。
