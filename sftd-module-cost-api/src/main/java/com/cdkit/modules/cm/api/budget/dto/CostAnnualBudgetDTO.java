package com.cdkit.modules.cm.api.budget.dto;

import com.cdkit.common.aspect.annotation.Dict;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 年度总预算DTO
 * <AUTHOR>
 * @date 2025-07-30
 */
@Schema(description = "年度总预算DTO")
@Data
public class CostAnnualBudgetDTO implements Serializable {
    private static final long serialVersionUID = 1L;

    /**UUID主键*/
    @Schema(description = "UUID主键")
    private String id;

    /**总预算编号(ZYS+4位年份+3位流水)*/
    @Schema(description = "总预算编号(ZYS+4位年份+3位流水)")
    private String budgetCode;

    /**总预算名称*/
    @Schema(description = "总预算名称")
    private String budgetName;

    /**年份*/
    @Schema(description = "年份")
    private String budgetYear;

    /**版本号*/
    @Schema(description = "版本号")
    private String version;

    /**状态(PENDING_LOCK-待锁定/APPROVING-审批中/LOCKED-已锁定/CHANGED-已变更)*/
    @Schema(description = "状态(PENDING_LOCK-待锁定/APPROVING-审批中/LOCKED-已锁定/CHANGED-已变更)")
    @Dict(dicCode = "cost_budget_status")
    private String budgetStatus;

    /**所属单位*/
    @Schema(description = "所属单位")
    private String professionalCompany;

    /**收入（不含税）总金额（元）*/
    @Schema(description = "收入（不含税）总金额（元）")
    private BigDecimal revenueTotalAmount;

    /**直接成本（不含税）总金额（元）*/
    @Schema(description = "直接成本（不含税）总金额（元）")
    private BigDecimal directCostTotalAmount;

    /**利润总额*/
    @Schema(description = "利润总额")
    private BigDecimal profitTotalAmount;

    /**利润率*/
    @Schema(description = "利润率")
    private BigDecimal profitRate;

    /**提交时间*/
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Schema(description = "提交时间")
    private Date submitTime;

    /**提交人*/
    @Schema(description = "提交人")
    private String submitBy;

    /**审批时间*/
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Schema(description = "审批时间")
    private Date approveTime;

    /**审批人*/
    @Schema(description = "审批人")
    private String approveBy;

    /**审批备注*/
    @Schema(description = "审批备注")
    private String approveRemark;

    /**父预算ID(变更时关联原预算)*/
    @Schema(description = "父预算ID(变更时关联原预算)")
    private String parentBudgetId;

    /**变更原因*/
    @Schema(description = "变更原因")
    private String changeReason;

    /**附件URL*/
    @Schema(description = "附件URL")
    private String attachmentUrl;

    /**创建时间*/
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Schema(description = "创建时间")
    private Date createTime;

    /**创建人*/
    @Schema(description = "创建人")
    private String createBy;

    /**更新时间*/
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Schema(description = "更新时间")
    private Date updateTime;

    /**更新人*/
    @Schema(description = "更新人")
    private String updateBy;

    /**租户ID*/
    @Schema(description = "租户ID")
    private Integer tenantId;

    /**删除标识 0:未删除 1:删除*/
    @Schema(description = "删除标识 0:未删除 1:删除")
    private Integer delFlag;

    /**所属部门代码*/
    @Schema(description = "所属部门代码")
    private String sysOrgCode;
}
