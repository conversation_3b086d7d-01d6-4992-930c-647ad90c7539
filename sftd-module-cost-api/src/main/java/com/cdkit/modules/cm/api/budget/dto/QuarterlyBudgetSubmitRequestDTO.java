package com.cdkit.modules.cm.api.budget.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import jakarta.validation.constraints.NotEmpty;
import java.util.List;

/**
 * 季度预算提交请求DTO
 * 支持单条提交和批量提交两种场景
 * 
 * <AUTHOR>
 * @date 2025-08-20
 */
@Data
@Schema(description = "季度预算提交请求DTO")
public class QuarterlyBudgetSubmitRequestDTO {

    /**
     * 季度预算ID列表
     * 批量提交已保存记录时使用，与budgetData二选一
     */
    @Schema(description = "季度预算ID列表（批量提交已保存记录时使用）",
            example = "[\"1234567890\", \"0987654321\"]")
    private List<String> budgetIds;

    /**
     * 季度预算数据
     * 新增提交时使用，与budgetIds二选一
     */
    @Schema(description = "季度预算数据（新增提交时使用）")
    private CostQuarterlyBudgetDTO budgetData;

    /**
     * 提交备注
     * 可选字段，用于记录提交时的说明信息
     */
    @Schema(description = "提交备注", example = "季度预算数据已完善，申请提交审批")
    private String submitRemark;

    /**
     * 是否强制提交
     * 当存在警告信息时，是否强制提交
     */
    @Schema(description = "是否强制提交", example = "false")
    private Boolean forceSubmit = false;

    /**
     * 获取提交的预算数量
     *
     * @return 预算数量
     */
    public int getBudgetCount() {
        if (isNewSubmit()) {
            return 1; // 新增提交算作1条
        }
        return budgetIds != null ? budgetIds.size() : 0;
    }

    /**
     * 是否为新增提交
     *
     * @return true-新增提交，false-已保存记录提交
     */
    public boolean isNewSubmit() {
        return budgetData != null;
    }

    /**
     * 是否为批量提交已保存记录
     *
     * @return true-批量提交，false-单条或新增提交
     */
    public boolean isBatchSubmit() {
        return budgetIds != null && budgetIds.size() > 1;
    }

    /**
     * 是否为单条提交已保存记录
     *
     * @return true-单条提交，false-批量或新增提交
     */
    public boolean isSingleSubmit() {
        return budgetIds != null && budgetIds.size() == 1;
    }

    /**
     * 获取第一个预算ID（用于单条提交场景）
     *
     * @return 第一个预算ID
     */
    public String getFirstBudgetId() {
        return (budgetIds != null && !budgetIds.isEmpty()) ? budgetIds.get(0) : null;
    }

    /**
     * 验证请求参数
     *
     * @return 验证错误信息，null表示验证通过
     */
    public String validateRequest() {
        // budgetIds和budgetData必须有且仅有一个
        boolean hasBudgetIds = budgetIds != null && !budgetIds.isEmpty();
        boolean hasBudgetData = budgetData != null;

        if (!hasBudgetIds && !hasBudgetData) {
            return "budgetIds和budgetData必须提供其中一个";
        }

        if (hasBudgetIds && hasBudgetData) {
            return "budgetIds和budgetData不能同时提供，请选择其中一种提交方式";
        }

        // 验证budgetIds
        if (hasBudgetIds) {
            for (String budgetId : budgetIds) {
                if (budgetId == null || budgetId.trim().isEmpty()) {
                    return "预算ID不能为空";
                }
            }
        }

        // 验证budgetData
        if (hasBudgetData) {
            if (budgetData.getQuarterlyBudgetName() == null || budgetData.getQuarterlyBudgetName().trim().isEmpty()) {
                return "季度预算名称不能为空";
            }
        }

        return null; // 验证通过
    }
}
