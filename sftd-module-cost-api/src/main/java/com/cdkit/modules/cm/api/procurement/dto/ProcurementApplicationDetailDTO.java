package com.cdkit.modules.cm.api.procurement.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.format.annotation.DateTimeFormat;

import jakarta.validation.constraints.DecimalMin;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 采购申请明细DTO
 * 
 * <AUTHOR>
 * @date 2025-08-26
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "采购申请明细")
public class ProcurementApplicationDetailDTO implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * UUID主键
     */
    @Schema(description = "UUID主键")
    private String id;

    /**
     * 关联采购申请主表ID
     */
    @Schema(description = "关联采购申请主表ID")
    private String applicationId;

    /**
     * 预算编码
     */
    @NotBlank(message = "预算编码不能为空")
    @Schema(description = "预算编码", example = "BUD001")
    private String budgetCode;

    /**
     * 预算名称
     */
    @NotBlank(message = "预算名称不能为空")
    @Schema(description = "预算名称", example = "原材料预算")
    private String budgetName;

    /**
     * 预算总量
     */
    @NotNull(message = "预算总量不能为空")
    @DecimalMin(value = "0", message = "预算总量不能为负数")
    @Schema(description = "预算总量", example = "500.0000")
    private BigDecimal budgetTotalQuantity;

    /**
     * 可采购量
     */
    @NotNull(message = "可采购量不能为空")
    @DecimalMin(value = "0", message = "可采购量不能为负数")
    @Schema(description = "可采购量", example = "350.0000")
    private BigDecimal availableQuantity;

    /**
     * 本次采购量
     */
    @NotNull(message = "本次采购量不能为空")
    @Schema(description = "本次采购量", example = "250.0000")
    private BigDecimal currentPurchaseQuantity;

    /**
     * 不含税单价（元）
     */
    @Schema(description = "不含税单价", example = "10.500000")
    private BigDecimal unitPriceExcludingTax;

    /**
     * 不含税总价（元）
     */
    @Schema(description = "不含税总价", example = "2625.00")
    private BigDecimal totalPriceExcludingTax;

    /**
     * 含税总价（元）
     */
    @Schema(description = "含税总价", example = "2966.25")
    private BigDecimal totalPriceIncludingTax;

    /**
     * 税额（元）
     */
    @Schema(description = "税额", example = "341.25")
    private BigDecimal taxAmount;

    /**
     * 关联采购执行依据明细表ID
     */
    @Schema(description = "关联采购执行依据明细表ID")
    private String executionBasisDetailId;

    /**
     * 关联季度预算主表ID
     */
    @Schema(description = "关联季度预算主表ID")
    private String quarterlyBudgetId;

    /**
     * 创建时间
     */
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Schema(description = "创建时间")
    private Date createTime;

    /**
     * 创建人
     */
    @Schema(description = "创建人")
    private String createBy;

    /**
     * 更新时间
     */
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Schema(description = "更新时间")
    private Date updateTime;

    /**
     * 更新人
     */
    @Schema(description = "更新人")
    private String updateBy;

    /**
     * 租户ID
     */
    @Schema(description = "租户ID")
    private Integer tenantId;

    /**
     * 删除标识
     */
    @Schema(description = "删除标识")
    private Integer delFlag;

    /**
     * 所属部门代码
     */
    @Schema(description = "所属部门代码")
    private String sysOrgCode;
}
