package com.cdkit.modules.cm.api.procurement.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.experimental.Accessors;
import org.springframework.format.annotation.DateTimeFormat;
import com.fasterxml.jackson.annotation.JsonFormat;

import java.io.Serializable;
import java.util.Date;

/**
 * 采购执行依据查询条件DTO
 * 
 * <AUTHOR>
 * @date 2025-08-25
 */
@Data
@Accessors(chain = true)
@Schema(description = "采购执行依据查询条件")
public class CostProcurementExecutionBasisQueryDTO implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 材料名称（支持模糊查询）
     */
    @Schema(description = "材料名称")
    private String materialName;

    /**
     * 材料编码（支持模糊查询）
     */
    @Schema(description = "材料编码")
    private String materialCode;

    /**
     * 执行日期（具体到日期）
     */
    @Schema(description = "执行日期")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private Date executionDate;

    /**
     * 所在季度
     */
    @Schema(description = "所在季度")
    private String quarter;

    /**
     * 开始时间（年月）
     */
    @Schema(description = "开始时间")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private Date startDate;

    /**
     * 结束时间（年月）
     */
    @Schema(description = "结束时间")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private Date endDate;
}
