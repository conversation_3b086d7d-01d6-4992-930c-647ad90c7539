package com.cdkit.modules.cm.api.budget.dto;

import com.cdkit.common.aspect.annotation.Dict;
import com.cdkitframework.poi.excel.annotation.Excel;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * 季度预算DTO
 * 
 * <AUTHOR>
 * @date 2025-08-12
 */
@Data
@Schema(description = "季度预算DTO")
public class CostQuarterlyBudgetDTO {

    /**UUID主键*/
    @Schema(description = "UUID主键")
    private String id;

    /**季度预算单号(JDYS+8位日期+3位流水)*/
    @Excel(name = "季度预算单号", width = 20)
    @Schema(description = "季度预算单号(JDYS+8位日期+3位流水)")
    private String quarterlyBudgetNo;

    /**季度预算编码(融合服务平台回传)*/
    @Excel(name = "季度预算编码", width = 20)
    @Schema(description = "季度预算编码(融合服务平台回传)")
    private String quarterlyBudgetCode;

    /**季度预算名称*/
    @Excel(name = "季度预算名称", width = 25)
    @Schema(description = "季度预算名称")
    private String quarterlyBudgetName;

    /**版本*/
    @Excel(name = "版本", width = 10)
    @Schema(description = "版本")
    private String version;

    /**状态(PENDING_LOCK-待锁定/APPROVING-审批中/LOCKED-已锁定/REJECTED-已驳回/CHANGED-已变更)*/
    @Excel(name = "状态", width = 15)
    @Schema(description = "状态(PENDING_LOCK-待锁定/APPROVING-审批中/LOCKED-已锁定/REJECTED-已驳回/CHANGED-已变更)")
    @Dict(dicCode = "cost_budget_status")
    private String budgetStatus;

    /**关联年度预算ID*/
    @Schema(description = "关联项目年度预算ID")
    private String annualBudgetId;

    /**关联年度预算编号*/
    @Excel(name = "关联年度预算编号", width = 20)
    @Schema(description = "关联年度预算编号")
    private String annualBudgetCode;

    /**WBS编号*/
    @Excel(name = "WBS编号", width = 15)
    @Schema(description = "WBS编号")
    private String wbsCode;

    /**下属中心*/
    @Excel(name = "下属中心", width = 15)
    @Schema(description = "下属中心")
    private String center;

    /**预算类型*/
    @Excel(name = "预算类型", width = 15)
    @Schema(description = "预算类型")
    private String budgetType;

    /**年度支出预算金额（不含税，元）*/
    @Excel(name = "年度支出预算金额（不含税，元）", width = 15)
    @Schema(description = "年度支出预算金额（不含税，元）")
    private java.math.BigDecimal annualExpenditureBudget;
    /**项目名称*/
    @Excel(name = "项目名称", width = 15)
    @Schema(description = "项目名称")
    private String projectName;
    /**关联季度计划ID*/
    @Schema(description = "关联季度计划ID")
    private String quarterlyPlanId;

    /**关联季度计划编号*/
    @Excel(name = "关联季度计划编号", width = 20)
    @Schema(description = "关联季度计划编号")
    private String quarterlyPlanCode;

    @Excel(name = "季度计划名称", width = 20)
    @Schema(description = "季度计划名称")
    private String quarterlyPlanName;

    /**所属单位*/
    @Excel(name = "所属单位", width = 20)
    @Schema(description = "所属单位")
    private String professionalCompany;

    /**客户名称*/
    @Excel(name = "客户名称", width = 15)
    @Schema(description = "客户名称")
    private String customerName;
    /**委托部门*/
    @Excel(name = "委托部门", width = 15)
    @Schema(description = "委托部门")
    private String entrustingDepartment;
    /**合同/任务名称*/
    @Excel(name = "合同/任务名称", width = 15)
    @Schema(description = "合同/任务名称")
    private String contractTaskName;
    /**合同/任务编号*/
    @Excel(name = "合同/任务编号", width = 15)
    @Schema(description = "合同/任务编号")
    private String contractTaskNo;
    /**合同签订/任务下发日期*/
    @Excel(name = "合同签订/任务下发日期", width = 15, format = "yyyy-MM-dd")
    @JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern="yyyy-MM-dd")
    @Schema(description = "合同签订/任务下发日期")
    private Date contractSignDate;
    /**合同/任务截止日期*/
    @Excel(name = "合同/任务截止日期", width = 15, format = "yyyy-MM-dd")
    @JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern="yyyy-MM-dd")
    @Schema(description = "合同/任务截止日期")
    private Date contractDeadline;

    @Schema(description = "预估收入金额（含税，元）")
    private java.math.BigDecimal estimatedRevenueWithTax;

    /**季度(如：2025年第一季度)*/
    @Excel(name = "季度(如：2025年第一季度)", width = 15)
    @Schema(description = "季度(如：2025年第一季度)")
    private String quarter;

    /**开始时间*/
    @Excel(name = "开始时间", width = 15, format = "yyyy-MM-dd")
    @JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern="yyyy-MM-dd")
    @Schema(description = "开始时间")
    private Date startDate;
    /**结束时间*/
    @Excel(name = "结束时间", width = 15, format = "yyyy-MM-dd")
    @JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern="yyyy-MM-dd")
    @Schema(description = "结束时间")
    private Date endDate;


    /**项目经理*/
    @Excel(name = "项目经理", width = 15)
    @Schema(description = "项目经理")
    private String projectManagerName;

    /**是否涉及多年预算(Y-是/N-否)*/
    @Excel(name = "是否涉及多年预算(Y-是/N-否)", width = 15)
    @Schema(description = "是否涉及多年预算(Y-是/N-否)")
    private String isMultiYearBudget;

    /**市场项目ID*/
    @Schema(description = "市场项目ID")
    private String marketProjectId;

    /**市场项目编号*/
    @Excel(name = "市场项目编号", width = 20)
    @Schema(description = "市场项目编号")
    private String marketProjectCode;

    /**市场项目名称*/
    @Excel(name = "市场项目名称", width = 25)
    @Schema(description = "市场项目名称")
    private String marketProjectName;

    /**年度收入预算金额（不含税，元）*/
    @Excel(name = "年度收入预算金额（不含税，元）", width = 15)
    @Schema(description = "年度收入预算金额（不含税，元）")
    private java.math.BigDecimal annualRevenueBudget;

   //-----------------------收入预算金额---------------------

    /**年度收入剩余预算金额（元）*/
    @Excel(name = "年度收入剩余预算金额（元）", width = 20)
    @Schema(description = "年度收入剩余预算金额（元）")
    private BigDecimal annualRevenueRemainingBudgetAmount;

    /**项目收入预算总额（元）*/
    @Excel(name = "项目收入预算总额（元）", width = 20)
    @Schema(description = "项目收入预算总额（元）")
    private BigDecimal projectRevenueBudgetTotalAmount;
//-----------------------支出预算金额
    /**年度支出剩余预算金额（元）*/
    @Excel(name = "年度支出剩余预算金额（元）", width = 20)
    @Schema(description = "年度支出剩余预算金额（元）")
    private BigDecimal annualExpenditureRemainingBudget;

    /**项目支出预算总额（元）*/
    @Excel(name = "项目支出预算总额（元）", width = 20)
    @Schema(description = "项目支出预算总额（元）")
    private BigDecimal projectExpenditureBudgetTotal;

    /**间接费预算总额（元）*/
    @Excel(name = "间接费预算总额（元）", width = 20)
    @Schema(description = "间接费预算总额（元）")
    private BigDecimal indirectCostBudgetTotal;
//-------------------------------------------
    /**项目边际利润（元）*/
    @Excel(name = "项目边际利润（元）", width = 20)
    @Schema(description = "项目边际利润（元）")
    private BigDecimal projectMarginalProfit;

    /**项目边际利润率*/
    @Excel(name = "项目边际利润率", width = 15)
    @Schema(description = "项目边际利润率")
    private BigDecimal projectMarginalProfitRate;

    /**项目净利润（元）*/
    @Excel(name = "项目净利润（元）", width = 20)
    @Schema(description = "项目净利润（元）")
    private BigDecimal projectNetProfit;

    /**项目净利润率*/
    @Excel(name = "项目净利润率", width = 15)
    @Schema(description = "项目净利润率")
    private BigDecimal projectNetProfitRate;

    /**工作流实例ID*/
    @Schema(description = "工作流实例ID")
    private String wiid;

    @Schema(description = "附件url")
    private String  attachmentUrl;

    /**备注*/
    @Excel(name = "备注", width = 30)
    @Schema(description = "备注")
    private String remark;

    /**创建时间*/
    @Excel(name = "创建时间", width = 20, format = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Schema(description = "创建时间")
    private Date createTime;

    /**创建人*/
    @Excel(name = "创建人", width = 15)
    @Schema(description = "创建人")
    private String createBy;

    /**更新时间*/
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Schema(description = "更新时间")
    private Date updateTime;

    /**更新人*/
    @Schema(description = "更新人")
    private String updateBy;

    /**租户ID*/
    @Schema(description = "租户ID")
    private String tenantId;

    // ==================== 子表明细数据 ====================

    /**采办包明细列表*/
    @Schema(description = "采办包明细列表")
    private List<CostQuarterlyBudgetProcPkgDetailDTO> procPkgDetailList;

    /**原材料明细列表*/
    @Schema(description = "原材料明细列表")
    private List<CostQuarterlyBudgetMaterialDetailDTO> materialDetailList;

    /**预算科目明细直接成本列表*/
    @Schema(description = "预算科目明细直接成本列表")
    private List<CostQuarterlyBudgetSubjectDirectCostDTO> subjectDirectCostList;

    /**本中心间接成本列表*/
    @Schema(description = "本中心间接成本列表")
    private List<CostQuarterlyBudgetCenterIndirectCostDTO> centerIndirectCostList;

    /**综合管理间接成本列表*/
    @Schema(description = "综合管理间接成本列表")
    private List<CostQuarterlyBudgetCompMageIndirectCostDTO> compMageIndirectCostList;

    /**非经营中心间接成本列表*/
    @Schema(description = "非经营中心间接成本列表")
    private List<CostQuarterlyBudgetNonOptCenterIndirectCostDTO> nonOptCenterIndirectCostList;

    /**收入明细列表*/
    @Schema(description = "收入明细列表")
    private List<CostQuarterlyBudgetRevenueDetailDTO> revenueDetailList;

    // ==================== 子表DTO内部类 ====================

    /**
     * 采办包明细DTO
     */
    @Schema(description = "采办包明细DTO")
    @Data
    public static class CostQuarterlyBudgetProcPkgDetailDTO {
        /**UUID主键*/
        @Schema(description = "UUID主键")
        private String id;

        /**关联季度预算主表ID*/
        @Schema(description = "关联季度预算主表ID")
        private String quarterlyBudgetId;

        /**直接成本（文本框，必填）*/
        @Schema(description = "直接成本（文本框，必填）")
        private String directCost;

        /**预算科目编码（关联预算科目表）*/
        @Schema(description = "预算科目编码（关联预算科目表）")
        private String budgetSubjectCode;

        /**预算科目名称*/
        @Schema(description = "预算科目名称")
        private String budgetSubjectName;

        /**金额（元，必填，支持小数）*/
        @Schema(description = "金额（元，必填，支持小数）")
        private BigDecimal amount;
    }

    /**
     * 原材料明细DTO
     */
    @Schema(description = "原材料明细DTO")
    @Data
    public static class CostQuarterlyBudgetMaterialDetailDTO {
        /**UUID主键*/
        @Schema(description = "UUID主键")
        private String id;

        /**关联季度预算主表ID*/
        @Schema(description = "关联季度预算主表ID")
        private String quarterlyBudgetId;

        /**物料编码*/
        @Schema(description = "物料编码")
        private String materialCode;

        /**物料名称*/
        @Schema(description = "物料名称")
        private String materialName;

        /**用量*/
        @Schema(description = "用量")
        private BigDecimal usageAmount;

        /**单位*/
        @Schema(description = "单位")
        private String unit;

        /**不含税单价(元)*/
        @Schema(description = "不含税单价(元)")
        private BigDecimal unitPriceExcludingTax;

        /**不含税总价(元)*/
        @Schema(description = "不含税总价(元)")
        private BigDecimal totalPriceExcludingTax;

        /**编制依据*/
        @Schema(description = "编制依据")
        private String compilationBasis;

        /**备注*/
        @Schema(description = "备注")
        private String remark;
    }

    /**
     * 预算科目明细直接成本DTO
     */
    @Schema(description = "预算科目明细直接成本DTO")
    @Data
    public static class CostQuarterlyBudgetSubjectDirectCostDTO {
        /**UUID主键*/
        @Schema(description = "UUID主键")
        private String id;

        /**关联季度预算主表ID*/
        @Schema(description = "关联季度预算主表ID")
        private String quarterlyBudgetId;

        /**直接成本-预算科目编码（关联预算科目表）*/
        @Schema(description = "直接成本-预算科目编码（关联预算科目表）")
        private String budgetSubjectCode;

        /**直接成本-预算科目名称*/
        @Schema(description = "直接成本-预算科目名称")
        private String budgetSubjectName;

        /**直接成本-科目释义*/
        @Schema(description = "直接成本-科目释义")
        private String subjectDescription;

        /**年度支出预算金额（不含税，元）*/
        @Schema(description = "年度支出预算金额（不含税，元）")
        private BigDecimal annualExpenditureBudgetAmount;

        /**年度剩余支出预算金额（不含税，元）*/
        @Schema(description = "年度剩余支出预算金额（不含税，元）")
        private BigDecimal annualRemainExpendBudget;

        /**间接费预算参考金额（不含税，元）*/
        @Schema(description = "间接费预算参考金额（不含税，元）")
        private BigDecimal indirectCostReferenceAmount;

        /**间接费预算金额（不含税，元）*/
        @Schema(description = "间接费预算金额（不含税，元）")
        private BigDecimal indirectCostBudgetAmount;

        /**支出预算金额（不含税，元）*/
        @Schema(description = "支出预算金额（不含税，元）")
        private BigDecimal expenditureBudgetAmount;
    }

    /**
     * 本中心间接成本DTO
     */
    @Schema(description = "本中心间接成本DTO")
    @Data
    public static class CostQuarterlyBudgetCenterIndirectCostDTO {
        /**UUID主键*/
        @Schema(description = "UUID主键")
        private String id;

        /**关联季度预算主表ID*/
        @Schema(description = "关联季度预算主表ID")
        private String quarterlyBudgetId;

        /**本中心间接成本-预算科目编码（关联预算科目表）*/
        @Schema(description = "本中心间接成本-预算科目编码（关联预算科目表）")
        private String budgetSubjectCode;

        /**本中心间接成本-预算科目名称*/
        @Schema(description = "本中心间接成本-预算科目名称")
        private String budgetSubjectName;

        /**本中心间接成本-预算释义*/
        @Schema(description = "本中心间接成本-预算释义")
        private String subjectDescription;

        /**本中心间接成本-支出预算金额（元）*/
        @Schema(description = "本中心间接成本-支出预算金额（元）")
        private BigDecimal expenditureBudgetAmount;
    }

    /**
     * 综合管理间接成本DTO
     */
    @Schema(description = "综合管理间接成本DTO")
    @Data
    public static class CostQuarterlyBudgetCompMageIndirectCostDTO {
        /**UUID主键*/
        @Schema(description = "UUID主键")
        private String id;

        /**关联季度预算主表ID*/
        @Schema(description = "关联季度预算主表ID")
        private String quarterlyBudgetId;

        /**综合管理间接成本-预算科目编码（关联预算科目表）*/
        @Schema(description = "综合管理间接成本-预算科目编码（关联预算科目表）")
        private String budgetSubjectCode;

        /**综合管理间接成本-预算科目名称*/
        @Schema(description = "综合管理间接成本-预算科目名称")
        private String budgetSubjectName;

        /**综合管理间接成本-预算释义*/
        @Schema(description = "综合管理间接成本-预算释义")
        private String subjectDescription;

        /**综合管理间接成本-支出预算金额（元）*/
        @Schema(description = "综合管理间接成本-支出预算金额（元）")
        private BigDecimal expenditureBudgetAmount;
    }

    /**
     * 非经营中心间接成本DTO
     */
    @Schema(description = "非经营中心间接成本DTO")
    @Data
    public static class CostQuarterlyBudgetNonOptCenterIndirectCostDTO {
        /**UUID主键*/
        @Schema(description = "UUID主键")
        private String id;

        /**关联季度预算主表ID*/
        @Schema(description = "关联季度预算主表ID")
        private String quarterlyBudgetId;

        /**非经营中心间接成本-预算科目编码（关联预算科目表）*/
        @Schema(description = "非经营中心间接成本-预算科目编码（关联预算科目表）")
        private String budgetSubjectCode;

        /**非经营中心间接成本-预算科目名称*/
        @Schema(description = "非经营中心间接成本-预算科目名称")
        private String budgetSubjectName;

        /**非经营中心间接成本-预算释义*/
        @Schema(description = "非经营中心间接成本-预算释义")
        private String subjectDescription;

        /**非经营中心间接成本-支出预算金额（元）*/
        @Schema(description = "非经营中心间接成本-支出预算金额（元）")
        private BigDecimal expenditureBudgetAmount;
    }

    /**
     * 收入明细DTO
     */
    @Schema(description = "收入明细DTO")
    @Data
    public static class CostQuarterlyBudgetRevenueDetailDTO {
        /**UUID主键*/
        @Schema(description = "UUID主键")
        private String id;

        /**关联季度预算主表ID*/
        @Schema(description = "关联季度预算主表ID")
        private String quarterlyBudgetId;

        /**物料编码*/
        @Schema(description = "物料编码")
        private String materialCode;

        /**物料名称*/
        @Schema(description = "物料名称")
        private String materialName;

        /**单位*/
        @Schema(description = "单位")
        private String unit;

        /**合同类型(lump_sum-总价合同/rate-费率合同)*/
        @Schema(description = "合同类型(lump_sum-总价合同/rate-费率合同)")
        private String contractType;

        /**产品数量*/
        @Schema(description = "产品数量")
        private BigDecimal productQuantity;

        /**预计年处理量（水/油）*/
        @Schema(description = "预计年处理量（水/油）")
        private BigDecimal estimatedAnnualProcessVolume;

        /**单价（元）*/
        @Schema(description = "单价（元）")
        private BigDecimal unitPrice;

        /**年度应收预算（元）*/
        @Schema(description = "年度应收预算（元）")
        private BigDecimal annualReceivableBudget;

        /**编制依据*/
        @Schema(description = "编制依据")
        private String compilationBasis;

        /**备注*/
        @Schema(description = "备注")
        private String remark;
    }
}
