package com.cdkit.modules.cm.api.procurement.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * 物料信息DTO
 * 用于物料选择下拉框等场景
 * 
 * <AUTHOR>
 * @date 2025-08-26
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "物料信息")
public class MaterialDTO implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 物料编码
     */
    @Schema(description = "物料编码", example = "MAT001")
    private String materialCode;

    /**
     * 物料名称
     */
    @Schema(description = "物料名称")
    private String materialName;

    /**
     * 计量单位
     */
    @Schema(description = "计量单位", example = "kg")
    private String unit;
}
