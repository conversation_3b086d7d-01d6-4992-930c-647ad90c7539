package com.cdkit.modules.cm.api.procurement.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.util.Date;

/**
 * 采购申请查询DTO
 * 
 * <AUTHOR>
 * @date 2025-08-26
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "采购申请查询条件")
public class ProcurementApplicationQueryDTO implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 采购申请单号（支持模糊查询）
     */
    @Schema(description = "采购申请单号", example = "PA202508260001")
    private String applicationNo;

    /**
     * 物料编码（支持模糊查询）
     */
    @Schema(description = "物料编码", example = "MAT001")
    private String materialCode;

    /**
     * 物料名称（支持模糊查询）
     */
    @Schema(description = "物料名称", example = "钢材")
    private String materialName;

    /**
     * 计量单位
     */
    @Schema(description = "计量单位", example = "kg")
    private String unit;

    /**
     * 所在季度
     */
    @Schema(description = "所在季度", example = "2025年第一季度")
    private String quarter;

    /**
     * 创建人
     */
    @Schema(description = "创建人", example = "admin")
    private String createBy;

    /**
     * 创建时间（用于日期范围查询）
     */
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @Schema(description = "创建时间", example = "2025-08-26")
    private Date createTime;

    /**
     * 创建开始时间
     */
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @Schema(description = "创建开始时间", example = "2025-08-01")
    private Date createTimeStart;

    /**
     * 创建结束时间
     */
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @Schema(description = "创建结束时间", example = "2025-08-31")
    private Date createTimeEnd;
}
