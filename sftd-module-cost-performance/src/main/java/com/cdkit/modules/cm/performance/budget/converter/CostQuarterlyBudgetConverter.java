package com.cdkit.modules.cm.performance.budget.converter;

import cn.hutool.core.bean.BeanUtil;
import com.cdkit.modules.cm.api.budget.dto.CostQuarterlyBudgetDTO;
import com.cdkit.modules.cm.api.budget.dto.CostQuarterlyBudgetMaterialDetailDTO;
import com.cdkit.modules.cm.api.budget.dto.ProcurementPackageSubjectDTO;
import com.cdkit.modules.cm.domain.budget.mode.entity.CostQuarterlyBudgetEntity;
import com.cdkit.modules.cm.domain.budget.mode.entity.CostQuarterlyBudgetSubjectDirectCostEntity;
import com.cdkit.modules.cm.domain.budget.service.ProcurementPackageQueryService;
import com.cdkit.modules.cm.domain.budget.service.QuarterlyBudgetDetailQueryService;
import com.cdkit.modules.cm.domain.budget.service.CenterIndirectCostInfo;
import com.cdkit.modules.cm.domain.budget.service.ComprehensiveIndirectCostInfo;
import com.cdkit.modules.cm.domain.budget.service.NonOperatingIndirectCostInfo;
import com.cdkit.modules.cm.domain.budget.service.SubjectDirectCostInfo;
import com.cdkit.modules.cm.domain.project.mode.entity.CostMaterialDetailEntity;

import java.math.BigDecimal;
import java.util.List;

/**
 * 季度预算表现层转换器
 * 负责领域实体和API DTO之间的转换
 * 
 * <AUTHOR>
 * @date 2025-08-18
 */
public class CostQuarterlyBudgetConverter {

    /**
     * DTO转换为领域实体
     *
     * @param dto DTO对象
     * @return 领域实体
     */
    public static CostQuarterlyBudgetEntity toEntity(CostQuarterlyBudgetDTO dto) {
        if (dto == null) {
            return null;
        }

        CostQuarterlyBudgetEntity entity = BeanUtil.copyProperties(dto, CostQuarterlyBudgetEntity.class);

        // 转换子表数据
        if (dto.getProcPkgDetailList() != null) {
            entity.setCostQuarterlyBudgetProcPkgDetailList(
                dto.getProcPkgDetailList().stream()
                    .map(CostQuarterlyBudgetConverter::toProcPkgDetailEntity)
                    .collect(java.util.stream.Collectors.toList())
            );
        }

        if (dto.getMaterialDetailList() != null) {
            entity.setCostQuarterlyBudgetMaterialDetailList(
                dto.getMaterialDetailList().stream()
                    .map(CostQuarterlyBudgetConverter::toMaterialDetailEntity)
                    .collect(java.util.stream.Collectors.toList())
            );
        }

        if (dto.getSubjectDirectCostList() != null) {
            entity.setCostQuarterlyBudgetSubjectDirectCostList(
                dto.getSubjectDirectCostList().stream()
                    .map(CostQuarterlyBudgetConverter::toSubjectDirectCostEntity)
                    .collect(java.util.stream.Collectors.toList())
            );
        }

        if (dto.getCenterIndirectCostList() != null) {
            entity.setCostQuarterlyBudgetCenterIndirectCostList(
                dto.getCenterIndirectCostList().stream()
                    .map(CostQuarterlyBudgetConverter::toCenterIndirectCostEntity)
                    .collect(java.util.stream.Collectors.toList())
            );
        }

        if (dto.getCompMageIndirectCostList() != null) {
            entity.setCostQuarterlyBudgetCompMageIndirectCostList(
                dto.getCompMageIndirectCostList().stream()
                    .map(CostQuarterlyBudgetConverter::toCompMageIndirectCostEntity)
                    .collect(java.util.stream.Collectors.toList())
            );
        }

        if (dto.getNonOptCenterIndirectCostList() != null) {
            entity.setCostQuarterlyBudgetNonOptCenterIndirectCostList(
                dto.getNonOptCenterIndirectCostList().stream()
                    .map(CostQuarterlyBudgetConverter::toNonOptCenterIndirectCostEntity)
                    .collect(java.util.stream.Collectors.toList())
            );
        }

        if (dto.getRevenueDetailList() != null) {
            entity.setCostQuarterlyBudgetRevenueDetailList(
                dto.getRevenueDetailList().stream()
                    .map(CostQuarterlyBudgetConverter::toRevenueDetailEntity)
                    .collect(java.util.stream.Collectors.toList())
            );
        }

        return entity;
    }

    /**
     * 领域实体转换为DTO
     * 
     * @param entity 领域实体
     * @return DTO对象
     */
    public static CostQuarterlyBudgetDTO toDTO(CostQuarterlyBudgetEntity entity) {
        if (entity == null) {
            return null;
        }
        return BeanUtil.copyProperties(entity, CostQuarterlyBudgetDTO.class);
    }

    /**
     * 领域实体列表转换为DTO列表
     * 
     * @param entityList 领域实体列表
     * @return DTO列表
     */
    public static List<CostQuarterlyBudgetDTO> toDTOList(List<CostQuarterlyBudgetEntity> entityList) {
        return BeanUtil.copyToList(entityList, CostQuarterlyBudgetDTO.class);
    }

    /**
     * DTO列表转换为领域实体列表
     * 
     * @param dtoList DTO列表
     * @return 领域实体列表
     */
    public static List<CostQuarterlyBudgetEntity> toEntityList(List<CostQuarterlyBudgetDTO> dtoList) {
        return BeanUtil.copyToList(dtoList, CostQuarterlyBudgetEntity.class);
    }

    /**
     * 原材料明细实体转换为DTO
     * 字段映射与CostQuarterlyBudgetMaterialDetail保持一致
     * 
     * @param entity 原材料明细实体
     * @return 季度预算原材料明细DTO
     */
    public static CostQuarterlyBudgetMaterialDetailDTO toMaterialDetailDTO(CostMaterialDetailEntity entity) {
        if (entity == null) {
            return null;
        }

        CostQuarterlyBudgetMaterialDetailDTO dto = new CostQuarterlyBudgetMaterialDetailDTO();
        
        // {{ Convert - 转换字段，保持与CostQuarterlyBudgetMaterialDetail一致. }}
        dto.setId(entity.getId());
        dto.setQuarterlyBudgetId(entity.getPlanId()); // planId映射为quarterlyBudgetId
        dto.setMaterialCode(entity.getMaterialCode());
        dto.setMaterialName(entity.getMaterialName());
        dto.setUsageAmount(entity.getUsageAmount());
        dto.setUnit(entity.getUnit());
        dto.setUnitPriceExcludingTax(entity.getUnitPriceExcludingTax());
        dto.setTotalPriceExcludingTax(entity.getTotalPriceExcludingTax());
        dto.setCompilationBasis(""); // 编制依据字段，CostMaterialDetail中没有，设为空
        dto.setRemark(entity.getRemark());
        dto.setCreateTime(entity.getCreateTime());
        dto.setCreateBy(entity.getCreateBy());
        dto.setUpdateTime(entity.getUpdateTime());
        dto.setUpdateBy(entity.getUpdateBy());
        dto.setTenantId(entity.getTenantId());
        dto.setDelFlag(entity.getDelFlag());
        dto.setSysOrgCode(entity.getSysOrgCode());

        return dto;
    }

    /**
     * 原材料明细实体列表转换为DTO列表
     * 
     * @param entityList 原材料明细实体列表
     * @return 季度预算原材料明细DTO列表
     */
    public static List<CostQuarterlyBudgetMaterialDetailDTO> toMaterialDetailDTOList(List<CostMaterialDetailEntity> entityList) {
        if (entityList == null || entityList.isEmpty()) {
            return List.of();
        }
        
        return entityList.stream()
                .map(CostQuarterlyBudgetConverter::toMaterialDetailDTO)
                .toList();
    }

    /**
     * 采办包预算科目信息转换为DTO
     *
     * @param subjectInfo 采办包预算科目信息
     * @return 采办包预算科目DTO
     */
    public static ProcurementPackageSubjectDTO toProcurementPackageSubjectDTO(
            ProcurementPackageQueryService.ProcurementPackageSubjectInfo subjectInfo) {
        if (subjectInfo == null) {
            return null;
        }

        ProcurementPackageSubjectDTO dto = new ProcurementPackageSubjectDTO();
        dto.setBudgetSubjectCode(subjectInfo.getBudgetSubjectCode());
        dto.setBudgetSubjectName(subjectInfo.getBudgetSubjectName());

        // 金额单位转换：数据库存储的是万元，API返回需要转换为元
        if (subjectInfo.getAmount() != null) {
            dto.setAmount(subjectInfo.getAmount().multiply(new BigDecimal("10000")));
        }

        return dto;
    }

    /**
     * 采办包预算科目信息列表转换为DTO列表
     * 
     * @param subjectInfoList 采办包预算科目信息列表
     * @return 采办包预算科目DTO列表
     */
    public static List<ProcurementPackageSubjectDTO> toProcurementPackageSubjectDTOList(
            List<ProcurementPackageQueryService.ProcurementPackageSubjectInfo> subjectInfoList) {
        if (subjectInfoList == null || subjectInfoList.isEmpty()) {
            return List.of();
        }
        
        return subjectInfoList.stream()
                .map(CostQuarterlyBudgetConverter::toProcurementPackageSubjectDTO)
                .toList();
    }

    // ==================== 子表转换方法 ====================

    /**
     * 采办包明细DTO转换为领域实体
     */
    public static com.cdkit.modules.cm.domain.budget.mode.entity.CostQuarterlyBudgetProcPkgDetailEntity toProcPkgDetailEntity(
            CostQuarterlyBudgetDTO.CostQuarterlyBudgetProcPkgDetailDTO dto) {
        if (dto == null) {
            return null;
        }
        return BeanUtil.copyProperties(dto, com.cdkit.modules.cm.domain.budget.mode.entity.CostQuarterlyBudgetProcPkgDetailEntity.class);
    }

    /**
     * 原材料明细DTO转换为领域实体
     */
    public static com.cdkit.modules.cm.domain.budget.mode.entity.CostQuarterlyBudgetMaterialDetailEntity toMaterialDetailEntity(
            CostQuarterlyBudgetDTO.CostQuarterlyBudgetMaterialDetailDTO dto) {
        if (dto == null) {
            return null;
        }
        return BeanUtil.copyProperties(dto, com.cdkit.modules.cm.domain.budget.mode.entity.CostQuarterlyBudgetMaterialDetailEntity.class);
    }

    /**
     * 预算科目直接成本DTO转换为领域实体
     */
    public static com.cdkit.modules.cm.domain.budget.mode.entity.CostQuarterlyBudgetSubjectDirectCostEntity toSubjectDirectCostEntity(
            CostQuarterlyBudgetDTO.CostQuarterlyBudgetSubjectDirectCostDTO dto) {
        if (dto == null) {
            return null;
        }
        return BeanUtil.copyProperties(dto, com.cdkit.modules.cm.domain.budget.mode.entity.CostQuarterlyBudgetSubjectDirectCostEntity.class);
    }

    /**
     * 本中心间接成本DTO转换为领域实体
     */
    public static com.cdkit.modules.cm.domain.budget.mode.entity.CostQuarterlyBudgetCenterIndirectCostEntity toCenterIndirectCostEntity(
            CostQuarterlyBudgetDTO.CostQuarterlyBudgetCenterIndirectCostDTO dto) {
        if (dto == null) {
            return null;
        }
        return BeanUtil.copyProperties(dto, com.cdkit.modules.cm.domain.budget.mode.entity.CostQuarterlyBudgetCenterIndirectCostEntity.class);
    }

    /**
     * 综合管理间接成本DTO转换为领域实体
     */
    public static com.cdkit.modules.cm.domain.budget.mode.entity.CostQuarterlyBudgetCompMageIndirectCostEntity toCompMageIndirectCostEntity(
            CostQuarterlyBudgetDTO.CostQuarterlyBudgetCompMageIndirectCostDTO dto) {
        if (dto == null) {
            return null;
        }
        return BeanUtil.copyProperties(dto, com.cdkit.modules.cm.domain.budget.mode.entity.CostQuarterlyBudgetCompMageIndirectCostEntity.class);
    }

    /**
     * 非经营中心间接成本DTO转换为领域实体
     */
    public static com.cdkit.modules.cm.domain.budget.mode.entity.CostQuarterlyBudgetNonOptCenterIndirectCostEntity toNonOptCenterIndirectCostEntity(
            CostQuarterlyBudgetDTO.CostQuarterlyBudgetNonOptCenterIndirectCostDTO dto) {
        if (dto == null) {
            return null;
        }
        return BeanUtil.copyProperties(dto, com.cdkit.modules.cm.domain.budget.mode.entity.CostQuarterlyBudgetNonOptCenterIndirectCostEntity.class);
    }

    /**
     * 收入明细DTO转换为领域实体
     */
    public static com.cdkit.modules.cm.domain.budget.mode.entity.CostQuarterlyBudgetRevenueDetailEntity toRevenueDetailEntity(
            CostQuarterlyBudgetDTO.CostQuarterlyBudgetRevenueDetailDTO dto) {
        if (dto == null) {
            return null;
        }
        return BeanUtil.copyProperties(dto, com.cdkit.modules.cm.domain.budget.mode.entity.CostQuarterlyBudgetRevenueDetailEntity.class);
    }

    // ==================== 领域对象转换为DTO方法 ====================

    /**
     * 采办包明细领域对象转换为DTO
     *
     * @param info 领域对象
     * @return DTO对象
     */
    public static CostQuarterlyBudgetDTO.CostQuarterlyBudgetProcPkgDetailDTO toProcPkgDetailDTOFromDomain(QuarterlyBudgetDetailQueryService.ProcPkgDetailInfo info) {
        if (info == null) {
            return null;
        }
        return BeanUtil.copyProperties(info, CostQuarterlyBudgetDTO.CostQuarterlyBudgetProcPkgDetailDTO.class);
    }

    /**
     * 采办包明细领域对象列表转换为DTO列表
     *
     * @param infoList 领域对象列表
     * @return DTO列表
     */
    public static List<CostQuarterlyBudgetDTO.CostQuarterlyBudgetProcPkgDetailDTO> toProcPkgDetailDTOListFromDomain(List<QuarterlyBudgetDetailQueryService.ProcPkgDetailInfo> infoList) {
        if (infoList == null || infoList.isEmpty()) {
            return List.of();
        }
        return infoList.stream()
                .map(CostQuarterlyBudgetConverter::toProcPkgDetailDTOFromDomain)
                .toList();
    }

    /**
     * 原材料明细领域对象转换为DTO
     *
     * @param info 领域对象
     * @return DTO对象
     */
    public static CostQuarterlyBudgetDTO.CostQuarterlyBudgetMaterialDetailDTO toMaterialDetailDTOFromDomain(QuarterlyBudgetDetailQueryService.MaterialDetailInfo info) {
        if (info == null) {
            return null;
        }
        return BeanUtil.copyProperties(info, CostQuarterlyBudgetDTO.CostQuarterlyBudgetMaterialDetailDTO.class);
    }

    /**
     * 原材料明细领域对象列表转换为DTO列表
     *
     * @param infoList 领域对象列表
     * @return DTO列表
     */
    public static List<CostQuarterlyBudgetDTO.CostQuarterlyBudgetMaterialDetailDTO> toMaterialDetailDTOListFromDomain(List<QuarterlyBudgetDetailQueryService.MaterialDetailInfo> infoList) {
        if (infoList == null || infoList.isEmpty()) {
            return List.of();
        }
        return infoList.stream()
                .map(CostQuarterlyBudgetConverter::toMaterialDetailDTOFromDomain)
                .toList();
    }

    /**
     * 预算科目明细直接成本领域对象转换为DTO
     *
     * @param info 领域对象
     * @return DTO对象
     */
    public static CostQuarterlyBudgetDTO.CostQuarterlyBudgetSubjectDirectCostDTO toSubjectDirectCostDTOFromDomain(QuarterlyBudgetDetailQueryService.SubjectDirectCostInfo info) {
        if (info == null) {
            return null;
        }
        return BeanUtil.copyProperties(info, CostQuarterlyBudgetDTO.CostQuarterlyBudgetSubjectDirectCostDTO.class);
    }

    /**
     * 预算科目明细直接成本领域对象列表转换为DTO列表
     *
     * @param infoList 领域对象列表
     * @return DTO列表
     */
    public static List<CostQuarterlyBudgetDTO.CostQuarterlyBudgetSubjectDirectCostDTO> toSubjectDirectCostDTOListFromDomain(List<CostQuarterlyBudgetSubjectDirectCostEntity> infoList) {
        if (infoList == null || infoList.isEmpty()) {
            return List.of();
        }
        return BeanUtil.copyToList(infoList,CostQuarterlyBudgetDTO.CostQuarterlyBudgetSubjectDirectCostDTO.class);
    }

    /**
     * 本中心间接成本领域对象转换为DTO
     *
     * @param info 领域对象
     * @return DTO对象
     */
    public static CostQuarterlyBudgetDTO.CostQuarterlyBudgetCenterIndirectCostDTO toCenterIndirectCostDTOFromDomain(QuarterlyBudgetDetailQueryService.CenterIndirectCostInfo info) {
        if (info == null) {
            return null;
        }
        return BeanUtil.copyProperties(info, CostQuarterlyBudgetDTO.CostQuarterlyBudgetCenterIndirectCostDTO.class);
    }

    /**
     * 本中心间接成本领域对象列表转换为DTO列表
     *
     * @param infoList 领域对象列表
     * @return DTO列表
     */
    public static List<CostQuarterlyBudgetDTO.CostQuarterlyBudgetCenterIndirectCostDTO> toCenterIndirectCostDTOListFromDomain(List<QuarterlyBudgetDetailQueryService.CenterIndirectCostInfo> infoList) {
        if (infoList == null || infoList.isEmpty()) {
            return List.of();
        }
        return infoList.stream()
                .map(CostQuarterlyBudgetConverter::toCenterIndirectCostDTOFromDomain)
                .toList();
    }

    /**
     * 综合管理间接成本领域对象转换为DTO
     *
     * @param info 领域对象
     * @return DTO对象
     */
    public static CostQuarterlyBudgetDTO.CostQuarterlyBudgetCompMageIndirectCostDTO toCompMageIndirectCostDTOFromDomain(QuarterlyBudgetDetailQueryService.ComprehensiveIndirectCostInfo info) {
        if (info == null) {
            return null;
        }
        return BeanUtil.copyProperties(info, CostQuarterlyBudgetDTO.CostQuarterlyBudgetCompMageIndirectCostDTO.class);
    }

    /**
     * 综合管理间接成本领域对象列表转换为DTO列表
     *
     * @param infoList 领域对象列表
     * @return DTO列表
     */
    public static List<CostQuarterlyBudgetDTO.CostQuarterlyBudgetCompMageIndirectCostDTO> toCompMageIndirectCostDTOListFromDomain(List<QuarterlyBudgetDetailQueryService.ComprehensiveIndirectCostInfo> infoList) {
        if (infoList == null || infoList.isEmpty()) {
            return List.of();
        }
        return infoList.stream()
                .map(CostQuarterlyBudgetConverter::toCompMageIndirectCostDTOFromDomain)
                .toList();
    }

    /**
     * 非经营中心间接成本领域对象转换为DTO
     *
     * @param info 领域对象
     * @return DTO对象
     */
    public static CostQuarterlyBudgetDTO.CostQuarterlyBudgetNonOptCenterIndirectCostDTO toNonOptCenterIndirectCostDTOFromDomain(QuarterlyBudgetDetailQueryService.NonOperatingIndirectCostInfo info) {
        if (info == null) {
            return null;
        }
        return BeanUtil.copyProperties(info, CostQuarterlyBudgetDTO.CostQuarterlyBudgetNonOptCenterIndirectCostDTO.class);
    }

    /**
     * 非经营中心间接成本领域对象列表转换为DTO列表
     *
     * @param infoList 领域对象列表
     * @return DTO列表
     */
    public static List<CostQuarterlyBudgetDTO.CostQuarterlyBudgetNonOptCenterIndirectCostDTO> toNonOptCenterIndirectCostDTOListFromDomain(List<QuarterlyBudgetDetailQueryService.NonOperatingIndirectCostInfo> infoList) {
        if (infoList == null || infoList.isEmpty()) {
            return List.of();
        }
        return infoList.stream()
                .map(CostQuarterlyBudgetConverter::toNonOptCenterIndirectCostDTOFromDomain)
                .toList();
    }

    /**
     * 收入明细领域对象转换为DTO
     *
     * @param info 领域对象
     * @return DTO对象
     */
    public static CostQuarterlyBudgetDTO.CostQuarterlyBudgetRevenueDetailDTO toRevenueDetailDTOFromDomain(QuarterlyBudgetDetailQueryService.RevenueDetailInfo info) {
        if (info == null) {
            return null;
        }
        return BeanUtil.copyProperties(info, CostQuarterlyBudgetDTO.CostQuarterlyBudgetRevenueDetailDTO.class);
    }

    /**
     * 收入明细领域对象列表转换为DTO列表
     *
     * @param infoList 领域对象列表
     * @return DTO列表
     */
    public static List<CostQuarterlyBudgetDTO.CostQuarterlyBudgetRevenueDetailDTO> toRevenueDetailDTOListFromDomain(List<QuarterlyBudgetDetailQueryService.RevenueDetailInfo> infoList) {
        if (infoList == null || infoList.isEmpty()) {
            return List.of();
        }
        return infoList.stream()
                .map(CostQuarterlyBudgetConverter::toRevenueDetailDTOFromDomain)
                .toList();
    }
}
