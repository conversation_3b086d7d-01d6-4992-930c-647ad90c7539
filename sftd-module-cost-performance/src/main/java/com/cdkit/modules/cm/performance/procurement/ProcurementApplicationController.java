package com.cdkit.modules.cm.performance.procurement;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.cdkit.common.api.vo.Result;
import com.cdkit.common.page.PageRes;
import com.cdkit.modules.cm.api.procurement.dto.ProcurementApplicationDTO;
import com.cdkit.modules.cm.api.procurement.dto.ProcurementApplicationQueryDTO;
import com.cdkit.modules.cm.application.procurement.ProcurementApplicationService;
import com.cdkit.modules.cm.application.procurement.converter.ProcurementApplicationDTOConverter;
import com.cdkit.modules.cm.domain.procurement.entity.ProcurementApplicationEntity;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.ModelAndView;

// Excel导出相关
import com.cdkitframework.poi.excel.def.NormalExcelConstants;
import com.cdkitframework.poi.excel.entity.ExportParams;
import com.cdkitframework.poi.excel.view.CdkitEntityExcelView;
import com.cdkit.common.exception.CdkitCloudException;
import com.cdkit.common.system.vo.LoginUser;
import org.apache.shiro.SecurityUtils;

import jakarta.validation.Valid;
import java.util.Arrays;
import java.util.List;

/**
 * 采购申请控制器
 * 
 * <AUTHOR>
 * @date 2025-08-26
 */
@Tag(name = "采购申请管理", description = "采购申请相关接口")
@RestController
@RequestMapping("/cm/procurementApplication")
@RequiredArgsConstructor
@Slf4j
public class ProcurementApplicationController {

    private final ProcurementApplicationService procurementApplicationService;

    /**
     * 分页查询采购申请列表
     * 支持申请单号、物料编码、物料名称模糊查询，季度、创建人精确查询
     * 按照创建时间倒序排列
     *
     * @param applicationNo 采购申请单号（支持模糊查询）
     * @param materialCode 物料编码（支持模糊查询）
     * @param materialName 物料名称（支持模糊查询）
     * @param quarter 所在季度
     * @param createBy 创建人
     * @param createTime 创建时间（具体到日期）
     * @param pageNo 页码
     * @param pageSize 每页数量
     * @return 分页结果
     */
    @Operation(summary = "采购申请-分页列表查询", description = "支持申请单号、物料编码、物料名称模糊查询，按创建时间倒序排列")
    @GetMapping("/list")
    public Result<IPage<ProcurementApplicationDTO>> queryPageList(
            @Parameter(description = "采购申请单号") @RequestParam(required = false) String applicationNo,
            @Parameter(description = "物料编码") @RequestParam(required = false) String materialCode,
            @Parameter(description = "物料名称") @RequestParam(required = false) String materialName,
            @Parameter(description = "所在季度") @RequestParam(required = false) String quarter,
            @Parameter(description = "创建人") @RequestParam(required = false) String createBy,
            @Parameter(description = "创建时间") @RequestParam(required = false) String createTime,
            @Parameter(description = "页码", example = "1") @RequestParam(name = "pageNo", defaultValue = "1") Integer pageNo,
            @Parameter(description = "每页数量", example = "10") @RequestParam(name = "pageSize", defaultValue = "10") Integer pageSize) {

        try {
            // 构建查询条件
            ProcurementApplicationQueryDTO queryDTO = ProcurementApplicationQueryDTO.builder()
                    .applicationNo(applicationNo)
                    .materialCode(materialCode)
                    .materialName(materialName)
                    .quarter(quarter)
                    .createBy(createBy)
                    .build();

            // 处理创建时间
            if (createTime != null && !createTime.trim().isEmpty()) {
                try {
                    java.text.SimpleDateFormat sdf = new java.text.SimpleDateFormat("yyyy-MM-dd");
                    queryDTO.setCreateTime(sdf.parse(createTime));
                } catch (Exception e) {
                    log.warn("创建时间格式错误: {}", createTime, e);
                }
            }

            // 转换为领域实体
            ProcurementApplicationEntity queryEntity = ProcurementApplicationDTOConverter.toEntity(queryDTO);

            // 执行查询
            PageRes<ProcurementApplicationEntity> pageRes = procurementApplicationService.queryPageList(queryEntity, pageNo, pageSize);

            // 构建返回结果
            IPage<ProcurementApplicationDTO> page = new Page<>(pageNo, pageSize);
            if (pageRes != null) {
                page.setCurrent(pageRes.getCurrent());
                page.setSize(pageRes.getSize());
                page.setTotal(pageRes.getTotal());
                page.setRecords(ProcurementApplicationDTOConverter.toDTOList(pageRes.getRecords()));
            }

            return Result.OK(page);

        } catch (Exception e) {
            log.error("查询采购申请列表失败", e);
            return Result.error("查询失败: " + e.getMessage());
        }
    }

    /**
     * 生成采购申请单号
     * 格式：PA + YYYYMMDD + 4位序号
     *
     * @return 采购申请单号
     */
    @Operation(summary = "采购申请-生成申请单号", description = "自动生成采购申请单号，格式：PA + YYYYMMDD + 4位序号")
    @GetMapping("/generateApplicationNo")
    public Result<String> generateApplicationNo() {
        try {
            String applicationNo = procurementApplicationService.generateApplicationNo();
            return Result.OK(applicationNo);
        } catch (Exception e) {
            log.error("生成采购申请单号失败", e);
            return Result.error("生成失败: " + e.getMessage());
        }
    }

    /**
     * 新增采购申请
     * 
     * @param dto 采购申请DTO
     * @return 保存后的采购申请信息
     */
    @Operation(summary = "采购申请-新增", description = "创建新的采购申请")
    @PostMapping
    public Result<String> create(@Valid @RequestBody ProcurementApplicationDTO dto) {
        try {
            // 转换为领域实体
            ProcurementApplicationEntity entity = ProcurementApplicationDTOConverter.toEntity(dto);
            
            // 保存
            ProcurementApplicationEntity savedEntity = procurementApplicationService.save(entity);

            return Result.OK("保存成功！");
            
        } catch (IllegalArgumentException e) {
            log.warn("新增采购申请参数错误: {}", e.getMessage());
            return Result.error(e.getMessage());
        } catch (Exception e) {
            log.error("新增采购申请失败", e);
            return Result.error("新增失败: " + e.getMessage());
        }
    }

    /**
     * 编辑采购申请
     *
     * @param dto 采购申请DTO
     * @return 更新后的采购申请信息
     */
    @Operation(summary = "采购申请-编辑", description = "修改现有的采购申请信息")
    @PutMapping("/edit")
    public Result<String> update(@Valid @RequestBody ProcurementApplicationDTO dto) {
        try {
            
            // 转换为领域实体
            ProcurementApplicationEntity entity = ProcurementApplicationDTOConverter.toEntity(dto);
            
            // 更新
            ProcurementApplicationEntity updatedEntity = procurementApplicationService.update(entity);
            

            return Result.OK("编辑成功！");
            
        } catch (IllegalArgumentException e) {
            log.warn("编辑采购申请参数错误: {}", e.getMessage());
            return Result.error(e.getMessage());
        } catch (Exception e) {
            log.error("编辑采购申请失败，ID: {}", dto.getId(), e);
            return Result.error("编辑失败: " + e.getMessage());
        }
    }

    /**
     * 数据回填接口
     * 根据采购申请ID查询完整的申请信息，用于编辑页面数据回填
     * 
     * @param id 采购申请ID
     * @return 采购申请详情
     */
    @Operation(summary = "采购申请-详情查询", description = "根据采购申请ID查询完整的申请信息，用于编辑页面数据回填")
    @GetMapping("/{id}")
    public Result<ProcurementApplicationDTO> getById(
            @Parameter(description = "采购申请ID", required = true) @PathVariable String id) {
        try {
            ProcurementApplicationEntity entity = procurementApplicationService.getById(id);
            if (entity == null) {
                return Result.error("采购申请不存在");
            }
            
            ProcurementApplicationDTO dto = ProcurementApplicationDTOConverter.toDTO(entity);
            return Result.OK(dto);
            
        } catch (IllegalArgumentException e) {
            log.warn("查询采购申请参数错误: {}", e.getMessage());
            return Result.error(e.getMessage());
        } catch (Exception e) {
            log.error("查询采购申请详情失败，ID: {}", id, e);
            return Result.error("查询失败: " + e.getMessage());
        }
    }

    /**
     * 删除采购申请
     * 
     * @param id 采购申请ID
     * @return 删除结果
     */
    @Operation(summary = "采购申请-删除", description = "根据ID删除采购申请")
    @DeleteMapping("/{id}")
    public Result<Void> deleteById(
            @Parameter(description = "采购申请ID", required = true) @PathVariable String id) {
        try {
            procurementApplicationService.deleteById(id);
            return Result.OK();
            
        } catch (IllegalArgumentException e) {
            log.warn("删除采购申请参数错误: {}", e.getMessage());
            return Result.error(e.getMessage());
        } catch (Exception e) {
            log.error("删除采购申请失败，ID: {}", id, e);
            return Result.error("删除失败: " + e.getMessage());
        }
    }

    /**
     * 批量删除采购申请
     * 
     * @param ids 采购申请ID列表
     * @return 删除结果
     */
    @Operation(summary = "采购申请-批量删除", description = "批量删除采购申请")
    @DeleteMapping("/batch")
    public Result<Void> deleteBatch(@RequestBody List<String> ids) {
        try {
            procurementApplicationService.deleteBatch(ids);
            return Result.OK();
            
        } catch (IllegalArgumentException e) {
            log.warn("批量删除采购申请参数错误: {}", e.getMessage());
            return Result.error(e.getMessage());
        } catch (Exception e) {
            log.error("批量删除采购申请失败", e);
            return Result.error("批量删除失败: " + e.getMessage());
        }
    }

    /**
     * 加权均分本次采购量到明细行
     * 
     * @param dto 采购申请DTO
     * @return 分配后的采购申请信息
     */
    @Operation(summary = "采购申请-加权均分", description = "将主表的本次采购量按预算总量比例分配到明细行")
    @PostMapping("/distributeQuantity")
    public Result<ProcurementApplicationDTO> distributeQuantityToDetails(@Valid @RequestBody ProcurementApplicationDTO dto) {

            // 转换为领域实体
            ProcurementApplicationEntity entity = ProcurementApplicationDTOConverter.toEntity(dto);
            
            // 执行加权均分
            ProcurementApplicationEntity distributedEntity = procurementApplicationService.distributeQuantityToDetails(entity);
            
            // 转换为DTO返回
            ProcurementApplicationDTO resultDTO = ProcurementApplicationDTOConverter.toDTO(distributedEntity);
            return Result.OK(resultDTO);
    }

    /**
     * 根据申请单号查询采购申请
     *
     * @param applicationNo 申请单号
     * @return 采购申请详情
     */
    @Operation(summary = "采购申请-根据申请单号查询", description = "根据申请单号查询采购申请详情")
    @GetMapping("/findByApplicationNo")
    public Result<ProcurementApplicationDTO> findByApplicationNo(
            @Parameter(description = "申请单号", required = true) @RequestParam String applicationNo) {
        try {
            ProcurementApplicationEntity entity = procurementApplicationService.findByApplicationNo(applicationNo);
            if (entity == null) {
                return Result.error("采购申请不存在");
            }

            ProcurementApplicationDTO dto = ProcurementApplicationDTOConverter.toDTO(entity);
            return Result.OK(dto);

        } catch (IllegalArgumentException e) {
            log.warn("根据申请单号查询采购申请参数错误: {}", e.getMessage());
            return Result.error(e.getMessage());
        } catch (Exception e) {
            log.error("根据申请单号查询采购申请失败，申请单号: {}", applicationNo, e);
            return Result.error("查询失败: " + e.getMessage());
        }
    }

    /**
     * 导出采购申请主表数据到Excel
     *
     * @param applicationNo 采购申请单号（支持模糊查询）
     * @param materialCode 物料编码（支持模糊查询）
     * @param materialName 物料名称（支持模糊查询）
     * @param quarter 所在季度
     * @param createBy 创建人
     * @param createTime 创建时间（具体到日期）
     * @return Excel文件
     */
    @Operation(summary = "采购申请-导出Excel", description = "导出符合条件的采购申请主表数据到Excel文件")
    @GetMapping("/exportXls")
    public ModelAndView exportXls(
            @Parameter(description = "采购申请单号") @RequestParam(required = false) String applicationNo,
            @Parameter(description = "物料编码") @RequestParam(required = false) String materialCode,
            @Parameter(description = "物料名称") @RequestParam(required = false) String materialName,
            @Parameter(description = "所在季度") @RequestParam(required = false) String quarter,
            @Parameter(description = "创建人") @RequestParam(required = false) String createBy,
            @Parameter(description = "创建时间") @RequestParam(required = false) String createTime) {

        try {
            // Step.1 获取当前用户信息
            LoginUser sysUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();
            String userName = "系统用户";
            if (sysUser != null) {
                userName = sysUser.getRealname();
            }

            // Step.2 构建查询条件
            ProcurementApplicationQueryDTO queryDTO = ProcurementApplicationQueryDTO.builder()
                    .applicationNo(applicationNo)
                    .materialCode(materialCode)
                    .materialName(materialName)
                    .quarter(quarter)
                    .createBy(createBy)
                    .build();

            // 处理创建时间
            if (createTime != null && !createTime.trim().isEmpty()) {
                try {
                    java.text.SimpleDateFormat sdf = new java.text.SimpleDateFormat("yyyy-MM-dd");
                    queryDTO.setCreateTime(sdf.parse(createTime));
                } catch (Exception e) {
                    log.warn("创建时间格式错误: {}", createTime, e);
                }
            }

            // Step.3 转换为领域实体
            ProcurementApplicationEntity queryEntity = ProcurementApplicationDTOConverter.toEntity(queryDTO);

            // Step.4 通过应用层获取导出数据（只包含主表数据，不包含明细）
            List<ProcurementApplicationEntity> entityList = procurementApplicationService.getMainTableExportList(queryEntity);

            // Step.5 转换为DTO列表（只包含主表信息）
            List<ProcurementApplicationDTO> exportList = ProcurementApplicationDTOConverter.toDTOList(entityList);

            // 确保导出列表不为null
            if (exportList == null) {
                exportList = Arrays.asList();
            }

            // Step.6 使用Cdkit包的导出逻辑 - 使用ProcurementApplicationDTO导出主表数据
            ModelAndView mv = new ModelAndView(new CdkitEntityExcelView());
            mv.addObject(NormalExcelConstants.FILE_NAME, "采购申请列表");
            mv.addObject(NormalExcelConstants.CLASS, ProcurementApplicationDTO.class);
            mv.addObject(NormalExcelConstants.PARAMS, new ExportParams("采购申请数据", "导出人:" + userName, "采购申请"));
            mv.addObject(NormalExcelConstants.DATA_LIST, exportList);

            log.info("导出采购申请Excel成功，共{}条数据", exportList.size());
            return mv;

        } catch (Exception e) {
            log.error("导出采购申请Excel失败", e);
            // 即使出现异常，也尝试导出空Excel
            try {
                LoginUser sysUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();
                String userName = sysUser != null ? sysUser.getRealname() : "系统用户";

                ModelAndView mv = new ModelAndView(new CdkitEntityExcelView());
                mv.addObject(NormalExcelConstants.FILE_NAME, "采购申请列表");
                mv.addObject(NormalExcelConstants.CLASS, ProcurementApplicationDTO.class);
                mv.addObject(NormalExcelConstants.PARAMS, new ExportParams("采购申请数据", "导出人:" + userName, "采购申请"));
                mv.addObject(NormalExcelConstants.DATA_LIST, Arrays.asList());
                log.warn("导出异常，返回空Excel文件");
                return mv;
            } catch (Exception ex) {
                log.error("导出空Excel也失败", ex);
                throw new CdkitCloudException("导出Excel失败：" + e.getMessage());
            }
        }
    }
}
