-- 采购执行依据数据库表设计
-- 创建时间: 2025-08-20
-- 描述: 采购执行依据主表和明细表，用于管理和查看物料采购执行情况

-- 采购执行依据主表
CREATE TABLE `cost_procurement_execution_basis`
(
    `id`                                VARCHAR(36)  NOT NULL COMMENT 'UUID主键',
    
    -- 物料信息
    `material_code`                     VARCHAR(50)  NOT NULL COMMENT '物料编码',
    `material_name`                     VARCHAR(100) NOT NULL COMMENT '物料名称',
    `unit`                              VARCHAR(20)  NOT NULL COMMENT '计量单位',
    
    -- 时间周期信息（来源于季度预算）
    `quarter`                           VARCHAR(20)  NOT NULL COMMENT '所在季度(如：2025年第一季度)',
    `start_date`                        DATE         NOT NULL COMMENT '开始时间（年月）',
    `end_date`                          DATE         NOT NULL COMMENT '结束时间（年月）',
    
    -- 需求汇总信息
    `total_demand_quantity`             DECIMAL(15, 4) NOT NULL DEFAULT 0.0000 COMMENT '总需求量',
    `unit_price_excluding_tax`          DECIMAL(15, 6) NOT NULL DEFAULT 0.000000 COMMENT '不含税单价（元）',
    `total_price_excluding_tax`         DECIMAL(15, 2) NOT NULL DEFAULT 0.00 COMMENT '不含税总价（元）',
    `total_price_including_tax`         DECIMAL(15, 2) NOT NULL DEFAULT 0.00 COMMENT '含税总价（元）',
    
    -- 已采购统计信息
    `purchased_quantity`                DECIMAL(15, 4) NOT NULL DEFAULT 0.0000 COMMENT '已采购量',
    `spent_amount_excluding_tax`        DECIMAL(15, 2) NOT NULL DEFAULT 0.00 COMMENT '已支出不含税金额（元）',
    `spent_amount_including_tax`        DECIMAL(15, 2) NOT NULL DEFAULT 0.00 COMMENT '已支出含税金额（元）',
    
    -- 剩余预算信息
    `remaining_amount_excluding_tax`    DECIMAL(15, 2) NOT NULL DEFAULT 0.00 COMMENT '剩余不含税金额（元）',
    `remaining_amount_including_tax`    DECIMAL(15, 2) NOT NULL DEFAULT 0.00 COMMENT '剩余含税金额（元）',
    
    -- 系统字段
    `create_time`                       DATETIME     NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `create_by`                         VARCHAR(50) COMMENT '创建人',
    `update_time`                       DATETIME     NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `update_by`                         VARCHAR(50) COMMENT '更新人',
    `tenant_id`                         INT          NOT NULL DEFAULT 0 COMMENT '租户ID',
    `del_flag`                          TINYINT      NOT NULL DEFAULT 0 COMMENT '删除标识 0:未删除 1:删除',
    `sys_org_code`                      VARCHAR(50) COMMENT '所属部门代码',
    
    PRIMARY KEY (`id`),
    UNIQUE KEY `uniq_material_period` (`material_code`, `start_date`, `end_date`, `tenant_id`, `del_flag`),
    KEY `idx_material_code` (`material_code`),
    KEY `idx_material_name` (`material_name`),
    KEY `idx_quarter` (`quarter`),
    KEY `idx_start_date` (`start_date`),
    KEY `idx_end_date` (`end_date`),
    KEY `idx_create_time` (`create_time`),
    KEY `idx_tenant_del` (`tenant_id`, `del_flag`)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4 COMMENT ='采购执行依据主表';

-- 采购执行依据明细表
CREATE TABLE `cost_procurement_execution_basis_detail`
(
    `id`                                VARCHAR(36)  NOT NULL COMMENT 'UUID主键',
    
    -- 关联信息
    `execution_basis_id`                VARCHAR(36)  NOT NULL COMMENT '关联采购执行依据主表ID',
    -- 预算信息
    `quarterly_budget_no`               VARCHAR(20)  NOT NULL COMMENT '预算编号',
    `quarterly_budget_name`             VARCHAR(100) NOT NULL COMMENT '预算名称',
    
    -- 物料需求明细
    `demand_quantity`                   DECIMAL(15, 4) NOT NULL DEFAULT 0.0000 COMMENT '总需求量',
    `unit`                              VARCHAR(20)  NOT NULL COMMENT '计量单位',
    `unit_price_excluding_tax`          DECIMAL(15, 6) NOT NULL DEFAULT 0.000000 COMMENT '不含税单价（元）',
    `total_price_excluding_tax`         DECIMAL(15, 2) NOT NULL DEFAULT 0.00 COMMENT '不含税总价（元）',
    `total_price_including_tax`         DECIMAL(15, 2) NOT NULL DEFAULT 0.00 COMMENT '含税总价（元）',
    
    -- 已采购统计信息
    `purchased_quantity`                DECIMAL(15, 4) NOT NULL DEFAULT 0.0000 COMMENT '已采购量',
    `spent_amount_excluding_tax`        DECIMAL(15, 2) NOT NULL DEFAULT 0.00 COMMENT '已支出不含税金额（元）',
    `spent_amount_including_tax`        DECIMAL(15, 2) NOT NULL DEFAULT 0.00 COMMENT '已支出含税金额（元）',
    
    -- 剩余预算信息
    `remaining_amount_excluding_tax`    DECIMAL(15, 2) NOT NULL DEFAULT 0.00 COMMENT '剩余不含税金额（元）',
    `remaining_amount_including_tax`    DECIMAL(15, 2) NOT NULL DEFAULT 0.00 COMMENT '剩余含税金额（元）',

    -- 系统字段
    `create_time`                       DATETIME     NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `create_by`                         VARCHAR(50) COMMENT '创建人',
    `update_time`                       DATETIME     NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `update_by`                         VARCHAR(50) COMMENT '更新人',
    `tenant_id`                         INT          NOT NULL DEFAULT 0 COMMENT '租户ID',
    `del_flag`                          TINYINT      NOT NULL DEFAULT 0 COMMENT '删除标识 0:未删除 1:删除',
    `sys_org_code`                      VARCHAR(50) COMMENT '所属部门代码',
    
    PRIMARY KEY (`id`),
    KEY `idx_execution_basis_id` (`execution_basis_id`),
    KEY `idx_quarterly_budget_no` (`quarterly_budget_no`),
    KEY `idx_create_time` (`create_time`),
    KEY `idx_tenant_del` (`tenant_id`, `del_flag`)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4 COMMENT ='采购执行依据明细表';

-- 添加表注释说明
ALTER TABLE `cost_procurement_execution_basis` COMMENT = '采购执行依据主表：用于管理物料采购执行情况的汇总信息，支持按物料和时间周期合并需求量，提供采购申请的数据基础';
ALTER TABLE `cost_procurement_execution_basis_detail` COMMENT = '采购执行依据明细表：记录每个季度预算项目下的物料需求明细，支持主子表展开查看具体预算信息和采购执行情况';